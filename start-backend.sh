#!/bin/bash

# Start Backend Server Script for SiteStrideManager
# This script ensures the backend API server starts properly on port 5001

echo "Starting SiteStrideManager Backend API..."

# Check if .NET is installed
if ! command -v dotnet &> /dev/null; then
    echo "Error: .NET is not installed. Please install .NET 8.0 or later."
    exit 1
fi

# Navigate to the backend directory
cd backend/SiteStrideManager.API

# Check if the project file exists
if [ ! -f "SiteStrideManager.API.csproj" ]; then
    echo "Error: SiteStrideManager.API.csproj not found. Please run this script from the project root directory."
    exit 1
fi

# Check if port 5001 is already in use
if lsof -i :5001 > /dev/null 2>&1; then
    echo "Warning: Port 5001 is already in use. Checking if it's our backend server..."
    if curl -s http://localhost:5001/swagger > /dev/null 2>&1; then
        echo "Backend server is already running on port 5001."
        echo "You can access the API documentation at: http://localhost:5001/swagger"
        exit 0
    else
        echo "Error: Port 5001 is occupied by another process. Please free the port and try again."
        exit 1
    fi
fi

# Start the backend server
echo "Starting the backend server on port 5001..."
echo "API documentation will be available at: http://localhost:5001/swagger"
echo "Press Ctrl+C to stop the server"
echo ""

dotnet run
