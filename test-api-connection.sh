#!/bin/bash

# API Connection Test Script
# This script tests the complete API connection flow

echo "Testing SiteStrideManager API Connection..."
echo "=========================================="

# Test 1: Check if backend is running
echo "1. Testing backend server availability..."
if curl -s http://localhost:5001/swagger > /dev/null 2>&1; then
    echo "✅ Backend server is running on port 5001"
else
    echo "❌ Backend server is not responding on port 5001"
    echo "   Please start the backend server first:"
    echo "   ./start-backend.sh"
    exit 1
fi

# Test 2: Test unauthenticated endpoint (should return 401)
echo ""
echo "2. Testing API endpoint authentication..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5001/api/sites)
if [ "$response" = "401" ]; then
    echo "✅ API authentication is working (401 Unauthorized as expected)"
else
    echo "❌ Unexpected response code: $response"
fi

# Test 3: Test user registration
echo ""
echo "3. Testing user registration..."
test_email="test-$(date +%s)@example.com"
register_response=$(curl -s -X POST http://localhost:5001/api/auth/register \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"$test_email\",\"password\":\"TestPassword123!\",\"firmName\":\"Test Construction\",\"address\":\"123 Test Street\",\"contact\":\"1234567890\"}")

if echo "$register_response" | grep -q "User registered successfully"; then
    echo "✅ User registration is working"
    
    # Test 4: Test user login
    echo ""
    echo "4. Testing user login..."
    login_response=$(curl -s -X POST http://localhost:5001/api/auth/login \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$test_email\",\"password\":\"TestPassword123!\"}")
    
    if echo "$login_response" | grep -q "token"; then
        echo "✅ User login is working"
        
        # Extract token for authenticated requests
        token=$(echo "$login_response" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
        
        # Test 5: Test authenticated endpoint
        echo ""
        echo "5. Testing authenticated API endpoints..."
        sites_response=$(curl -s -H "Authorization: Bearer $token" http://localhost:5001/api/sites)
        if [ $? -eq 0 ]; then
            echo "✅ Authenticated API endpoints are working"
            echo "   Sites endpoint returned: $sites_response"
        else
            echo "❌ Authenticated API endpoints failed"
        fi
        
    else
        echo "❌ User login failed"
        echo "   Response: $login_response"
    fi
    
else
    echo "❌ User registration failed"
    echo "   Response: $register_response"
fi

echo ""
echo "=========================================="
echo "API Connection Test Complete"
echo ""
echo "If all tests passed, your backend API is working correctly."
echo "You can now start the frontend with: npm run dev"
