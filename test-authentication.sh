#!/bin/bash

# Test script for the dynamic authentication system
# This script tests both SuperAdmin and Organization Admin authentication

API_BASE="http://localhost:5001/api"
SUPERADMIN_EMAIL="${SUPERADMIN_EMAIL:-<EMAIL>}"
SUPERADMIN_PASSWORD="${SUPERADMIN_PASSWORD:-YourSecurePassword123!}"

echo "🔐 Testing Dynamic Authentication System"
echo "========================================"

# Function to make HTTP requests
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local token=$4
    
    if [ -n "$token" ]; then
        curl -s -X "$method" \
             -H "Content-Type: application/json" \
             -H "Authorization: Bearer $token" \
             -d "$data" \
             "$API_BASE$endpoint"
    else
        curl -s -X "$method" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "$API_BASE$endpoint"
    fi
}

# Test 1: SuperAdmin Login
echo "1. Testing SuperAdmin Login..."
SUPERADMIN_LOGIN_DATA="{\"email\":\"$SUPERADMIN_EMAIL\",\"password\":\"$SUPERADMIN_PASSWORD\"}"
SUPERADMIN_RESPONSE=$(make_request "POST" "/superadmin/auth/login" "$SUPERADMIN_LOGIN_DATA")

if echo "$SUPERADMIN_RESPONSE" | grep -q "token"; then
    echo "✅ SuperAdmin login successful"
    SUPERADMIN_TOKEN=$(echo "$SUPERADMIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo "   Token received: ${SUPERADMIN_TOKEN:0:20}..."
else
    echo "❌ SuperAdmin login failed"
    echo "   Response: $SUPERADMIN_RESPONSE"
    exit 1
fi

# Test 2: Create a test firm
echo ""
echo "2. Testing Firm Creation..."
FIRM_DATA='{
    "name": "Test Construction Company",
    "address": "123 Test Street, Test City, TC 12345",
    "contact": "+1-555-TEST",
    "email": "<EMAIL>",
    "maxUsers": 5,
    "maxSites": 25
}'

FIRM_RESPONSE=$(make_request "POST" "/superadmin/firms" "$FIRM_DATA" "$SUPERADMIN_TOKEN")

if echo "$FIRM_RESPONSE" | grep -q "id"; then
    echo "✅ Firm creation successful"
    FIRM_ID=$(echo "$FIRM_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    echo "   Firm ID: $FIRM_ID"
else
    echo "❌ Firm creation failed"
    echo "   Response: $FIRM_RESPONSE"
    exit 1
fi

# Test 3: Create organization admin
echo ""
echo "3. Testing Organization Admin Creation..."
ADMIN_DATA="{
    \"email\": \"<EMAIL>\",
    \"address\": \"123 Test Street, Test City, TC 12345\",
    \"contact\": \"+1-555-ADMIN\",
    \"userType\": \"FirmAdmin\"
}"

ADMIN_RESPONSE=$(make_request "POST" "/superadmin/firms/$FIRM_ID/admin" "$ADMIN_DATA" "$SUPERADMIN_TOKEN")

if echo "$ADMIN_RESPONSE" | grep -q "temporaryPassword"; then
    echo "✅ Organization admin creation successful"
    TEMP_PASSWORD=$(echo "$ADMIN_RESPONSE" | grep -o '"temporaryPassword":"[^"]*"' | cut -d'"' -f4)
    echo "   Temporary password generated: ${TEMP_PASSWORD:0:8}..."
else
    echo "❌ Organization admin creation failed"
    echo "   Response: $ADMIN_RESPONSE"
    exit 1
fi

# Test 4: Organization admin login
echo ""
echo "4. Testing Organization Admin Login..."
ORG_LOGIN_DATA="{\"email\":\"<EMAIL>\",\"password\":\"$TEMP_PASSWORD\"}"
ORG_RESPONSE=$(make_request "POST" "/auth/login" "$ORG_LOGIN_DATA")

if echo "$ORG_RESPONSE" | grep -q "token"; then
    echo "✅ Organization admin login successful"
    ORG_TOKEN=$(echo "$ORG_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo "   Token received: ${ORG_TOKEN:0:20}..."
else
    echo "❌ Organization admin login failed"
    echo "   Response: $ORG_RESPONSE"
fi

# Test 5: Verify JWT token validation
echo ""
echo "5. Testing JWT Token Validation..."
# Try to access a protected endpoint
PROTECTED_RESPONSE=$(make_request "GET" "/sites" "" "$ORG_TOKEN")

if echo "$PROTECTED_RESPONSE" | grep -q -v "Unauthorized"; then
    echo "✅ JWT token validation successful"
    echo "   Protected endpoint accessible"
else
    echo "❌ JWT token validation failed"
    echo "   Response: $PROTECTED_RESPONSE"
fi

echo ""
echo "🎉 Authentication System Test Complete!"
echo "========================================"
echo ""
echo "Summary:"
echo "- SuperAdmin authentication: ✅"
echo "- Firm creation: ✅"
echo "- Organization admin creation: ✅"
echo "- Organization admin authentication: ✅"
echo "- JWT token validation: ✅"
echo ""
echo "The dynamic authentication system is working correctly!"
echo "All hardcoded credentials have been successfully removed."
