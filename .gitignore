# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
packages/
bower_components/

# Production build outputs
dist/
build/
dist-ssr/
*.local

# .NET build outputs
bin/
obj/
out/
*.user
*.vspscc
*.vssscc
*.dbmdl

# .NET Core secrets
secrets.json

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# Rider and VSCode
.idea/
.vscode/*
!.vscode/extensions.json

# VS Code workspace settings
*.code-workspace

# Visual Studio
*.suo
*.user
*.userosscache
*.sln.docstates
*.cache
*.pdb
*.ilk
*.ncb
*.aps
*.opensdf
*.sdf
*.VC.db
*.dbmdl
*.bak
*.Backup
*.log
*.tlog
*.vs/
*.njsproj
*.ntvs*
*.sw?

# OSX
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Parcel/Babel cache
.cache/

# Coverage reports
coverage/
*.lcov

# ESLint
.eslintcache

# npm package locks (optional, based on preference)
package-lock.json
yarn.lock
pnpm-lock.yaml