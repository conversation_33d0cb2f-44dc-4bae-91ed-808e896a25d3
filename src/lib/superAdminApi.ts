const API_BASE_URL = 'http://localhost:5001/api/superadmin';

class SuperAdminApiClient {
  private getAuthHeaders() {
    const token = localStorage.getItem('superadmin_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  }

  // Authentication
  async login(email: string, password: string) {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    return response.json();
  }

  async getCurrentSuperAdmin() {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to get current super admin');
    }

    return response.json();
  }

  async changePassword(currentPassword: string, newPassword: string) {
    const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ currentPassword, newPassword }),
    });

    if (!response.ok) {
      throw new Error('Failed to change password');
    }

    return response.json();
  }

  // Firm Management
  async getAllFirms() {
    const response = await fetch(`${API_BASE_URL}/firms`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch firms');
    }

    return response.json();
  }

  async getFirmById(firmId: string) {
    const response = await fetch(`${API_BASE_URL}/firms/${firmId}`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch firm');
    }

    return response.json();
  }

  async createFirm(firmData: any) {
    const response = await fetch(`${API_BASE_URL}/firms`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(firmData),
    });

    if (!response.ok) {
      throw new Error('Failed to create firm');
    }

    return response.json();
  }

  async updateFirm(firmId: string, firmData: any) {
    const response = await fetch(`${API_BASE_URL}/firms/${firmId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(firmData),
    });

    if (!response.ok) {
      throw new Error('Failed to update firm');
    }

    return response.json();
  }

  async activateFirm(firmId: string) {
    const response = await fetch(`${API_BASE_URL}/firms/${firmId}/activate`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to activate firm');
    }

    return response.json();
  }

  async deactivateFirm(firmId: string) {
    const response = await fetch(`${API_BASE_URL}/firms/${firmId}/deactivate`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to deactivate firm');
    }

    return response.json();
  }

  async getFirmUsers(firmId: string) {
    const response = await fetch(`${API_BASE_URL}/firms/${firmId}/users`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch firm users');
    }

    return response.json();
  }

  async createFirmAdmin(firmId: string, adminData: any) {
    const response = await fetch(`${API_BASE_URL}/firms/${firmId}/admin`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(adminData),
    });

    if (!response.ok) {
      throw new Error('Failed to create firm admin');
    }

    return response.json();
  }

  async getDashboardStats() {
    const response = await fetch(`${API_BASE_URL}/firms/dashboard/stats`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch dashboard stats');
    }

    return response.json();
  }
}

export const superAdminApiClient = new SuperAdminApiClient();
