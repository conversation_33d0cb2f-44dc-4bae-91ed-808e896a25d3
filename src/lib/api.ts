const API_BASE_URL = 'http://localhost:5001/api';

class ApiClient {
  private requestCache = new Map<string, Promise<any>>();
  private requestTimestamps = new Map<string, number>();
  private readonly CACHE_DURATION = 5000; // 5 seconds

  private getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const token = this.getAuthToken();

    // Check if token is required but missing
    if (!token && !endpoint.includes('/auth/')) {
      console.warn('No authentication token found for protected endpoint:', url);
      throw new Error('Authentication required. Please log in.');
    }

    // Create cache key
    const cacheKey = `${options.method || 'GET'}:${url}:${JSON.stringify(options.body || '')}`;
    const now = Date.now();

    // Check if we have a recent cached request
    const cachedTimestamp = this.requestTimestamps.get(cacheKey);
    if (cachedTimestamp && (now - cachedTimestamp) < this.CACHE_DURATION) {
      const cachedRequest = this.requestCache.get(cacheKey);
      if (cachedRequest) {
        console.log('Returning cached request for:', url);
        return cachedRequest;
      }
    }

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    // Create and cache the request promise
    const requestPromise = (async () => {
      try {
        const response = await fetch(url, { ...config, signal: controller.signal });
        clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 401) {
          console.error('Authentication failed - clearing tokens and redirecting to login');
          // Token expired or invalid, clear it
          localStorage.removeItem('auth_token');
          localStorage.removeItem('constructionApp_user');
          // Instead of reloading, redirect to login page
          window.location.href = '/login';
          throw new Error('Authentication failed');
        }

        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (jsonError) {
          // If we can't parse the error response, use the status text
          errorMessage = response.statusText || errorMessage;
        }

        console.error(`API Error: ${errorMessage}`);
        throw new Error(errorMessage);
      }

      // Handle empty responses (like 204 No Content)
      if (response.status === 204) {
        return {} as T;
      }

        const data = await response.json();
        return data;
      } catch (error) {
        clearTimeout(timeoutId);

        // Handle timeout errors
        if (error instanceof Error && error.name === 'AbortError') {
          console.error('API request timed out:', url);
          throw new Error('Request timed out. Please check your connection and try again.');
        }

        // Enhanced error logging
        console.error('API request failed:', {
          url,
          method: config.method || 'GET',
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        });

        // Re-throw with more context for network errors
        if (error instanceof TypeError && error.message.includes('fetch')) {
          throw new Error('Network error: Unable to connect to the server. Please check if the backend is running.');
        }

        throw error;
      }
    })();

    // Cache the request
    this.requestCache.set(cacheKey, requestPromise);
    this.requestTimestamps.set(cacheKey, now);

    // Clean up old cache entries
    setTimeout(() => {
      this.requestCache.delete(cacheKey);
      this.requestTimestamps.delete(cacheKey);
    }, this.CACHE_DURATION);

    return requestPromise;
  }

  // Auth endpoints
  async login(email: string, password: string) {
    return this.request<{ token: string; user: any }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(userData: any) {
    return this.request<{ message: string }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async changePassword(currentPassword: string, newPassword: string) {
    return this.request<{ message: string }>('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({ currentPassword, newPassword }),
    });
  }

  async updateProfile(userData: any) {
    return this.request<any>('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async getCurrentUser() {
    return this.request<any>('/auth/me');
  }

  // Sites endpoints
  async getSites() {
    return this.request<any[]>('/sites');
  }

  async getSite(id: string) {
    return this.request<any>(`/sites/${id}`);
  }

  async createSite(siteData: any) {
    return this.request<any>('/sites', {
      method: 'POST',
      body: JSON.stringify(siteData),
    });
  }

  async updateSite(id: string, siteData: any) {
    return this.request<any>(`/sites/${id}`, {
      method: 'PUT',
      body: JSON.stringify(siteData),
    });
  }

  async deleteSite(id: string) {
    return this.request<void>(`/sites/${id}`, {
      method: 'DELETE',
    });
  }

  async getMaterialEntries(siteId: string) {
    if (!siteId) {
      throw new Error('Site ID is required');
    }

    try {
      const response = await this.request<any[]>(`/sites/${siteId}/materials`);
      // Ensure response is always an array
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error(`Failed to get material entries for site ${siteId}:`, error);
      throw error;
    }
  }

  async createMaterialEntry(entryData: any) {
    if (!entryData || !entryData.siteId) {
      throw new Error('Material entry data and site ID are required');
    }

    return this.request<any>('/sites/materials', {
      method: 'POST',
      body: JSON.stringify(entryData),
    });
  }

  async getProgressEntries(siteId: string) {
    if (!siteId) {
      throw new Error('Site ID is required');
    }

    try {
      const response = await this.request<any[]>(`/sites/${siteId}/progress`);
      // Ensure response is always an array
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error(`Failed to get progress entries for site ${siteId}:`, error);
      throw error;
    }
  }

  async createProgressEntry(entryData: any) {
    if (!entryData || !entryData.siteId) {
      throw new Error('Progress entry data and site ID are required');
    }

    return this.request<any>('/sites/progress', {
      method: 'POST',
      body: JSON.stringify(entryData),
    });
  }

  // Personal expenses endpoints
  async getPersonalExpenses() {
    return this.request<any[]>('/personalexpenses');
  }

  async createPersonalExpense(expenseData: any) {
    return this.request<any>('/personalexpenses', {
      method: 'POST',
      body: JSON.stringify(expenseData),
    });
  }

  async updatePersonalExpense(id: string, expenseData: any) {
    return this.request<any>(`/personalexpenses/${id}`, {
      method: 'PUT',
      body: JSON.stringify(expenseData),
    });
  }

  async deletePersonalExpense(id: string) {
    return this.request<void>(`/personalexpenses/${id}`, {
      method: 'DELETE',
    });
  }
}

export const apiClient = new ApiClient();
