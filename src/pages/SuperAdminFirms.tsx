import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Building2, 
  Users, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Plus, 
  Eye, 
  MoreHorizontal,
  ArrowLeft,
  Search
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Firm {
  id: string;
  name: string;
  description?: string;
  address: string;
  contact: string;
  email?: string;
  logoUrl?: string;
  website?: string;
  taxId?: string;
  registrationNumber?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  subscriptionStatus: string;
  subscriptionExpiryDate?: string;
  maxUsers: number;
  maxSites: number;
}

interface NewFirm {
  name: string;
  description: string;
  address: string;
  contact: string;
  email: string;
  website: string;
  maxUsers: number;
  maxSites: number;
}

const SuperAdminFirms: React.FC = () => {
  const [firms, setFirms] = useState<Firm[]>([]);
  const [filteredFirms, setFilteredFirms] = useState<Firm[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newFirm, setNewFirm] = useState<NewFirm>({
    name: '',
    description: '',
    address: '',
    contact: '',
    email: '',
    website: '',
    maxUsers: 10,
    maxSites: 50
  });

  const navigate = useNavigate();
  const { toast } = useToast();

  const toggleFirmStatus = async (firmId: string, currentStatus: boolean) => {
    try {
      const token = localStorage.getItem('superadmin_token');
      const endpoint = currentStatus ? 'deactivate' : 'activate';

      const response = await fetch(`http://localhost:5001/api/superadmin/firms/${firmId}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: `Firm ${currentStatus ? 'deactivated' : 'activated'} successfully`,
        });
        fetchFirms();
      } else {
        throw new Error('Failed to update firm status');
      }
    } catch (error) {
      console.error('Error updating firm status:', error);
      toast({
        title: "Error",
        description: "Failed to update firm status",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string, isActive: boolean) => {
    if (!isActive) {
      return <Badge variant="destructive">Inactive</Badge>;
    }

    switch (status.toLowerCase()) {
      case 'active':
        return <Badge variant="default">Active</Badge>;
      case 'suspended':
        return <Badge variant="secondary">Suspended</Badge>;
      case 'expired':
        return <Badge variant="outline">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  useEffect(() => {
    fetchFirms();
  }, []);

  useEffect(() => {
    const filtered = firms.filter(firm =>
      firm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      firm.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      firm.contact.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredFirms(filtered);
  }, [firms, searchTerm]);

  const fetchFirms = async () => {
    try {
      const token = localStorage.getItem('superadmin_token');
      if (!token) {
        navigate('/superadmin/login');
        return;
      }

      const response = await fetch('http://localhost:5001/api/superadmin/firms', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFirms(data);
      } else {
        throw new Error('Failed to fetch firms');
      }
    } catch (error) {
      console.error('Error fetching firms:', error);
      toast({
        title: "Error",
        description: "Failed to load firms",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateFirm = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const token = localStorage.getItem('superadmin_token');
      const response = await fetch('http://localhost:5001/api/superadmin/firms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(newFirm),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Firm created successfully",
        });
        setShowCreateDialog(false);
        setNewFirm({
          name: '',
          description: '',
          address: '',
          contact: '',
          email: '',
          website: '',
          maxUsers: 10,
          maxSites: 50
        });
        fetchFirms();
      } else {
        throw new Error('Failed to create firm');
      }
    } catch (error) {
      console.error('Error creating firm:', error);
      toast({
        title: "Error",
        description: "Failed to create firm",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading firms...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Button
                variant="ghost"
                onClick={() => navigate('/superadmin/dashboard')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Firm Management</h1>
                <p className="text-sm text-gray-500">Manage construction firms and their subscriptions</p>
              </div>
            </div>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Firm
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Create New Firm</DialogTitle>
                  <DialogDescription>
                    Add a new construction firm to the system.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateFirm}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Firm Name *</Label>
                        <Input
                          id="name"
                          value={newFirm.name}
                          onChange={(e) => setNewFirm({...newFirm, name: e.target.value})}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="contact">Contact Number *</Label>
                        <Input
                          id="contact"
                          value={newFirm.contact}
                          onChange={(e) => setNewFirm({...newFirm, contact: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="address">Address *</Label>
                      <Textarea
                        id="address"
                        value={newFirm.address}
                        onChange={(e) => setNewFirm({...newFirm, address: e.target.value})}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={newFirm.email}
                          onChange={(e) => setNewFirm({...newFirm, email: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="website">Website</Label>
                        <Input
                          id="website"
                          value={newFirm.website}
                          onChange={(e) => setNewFirm({...newFirm, website: e.target.value})}
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={newFirm.description}
                        onChange={(e) => setNewFirm({...newFirm, description: e.target.value})}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="maxUsers">Max Users</Label>
                        <Input
                          id="maxUsers"
                          type="number"
                          value={newFirm.maxUsers}
                          onChange={(e) => setNewFirm({...newFirm, maxUsers: parseInt(e.target.value)})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="maxSites">Max Sites</Label>
                        <Input
                          id="maxSites"
                          type="number"
                          value={newFirm.maxSites}
                          onChange={(e) => setNewFirm({...newFirm, maxSites: parseInt(e.target.value)})}
                        />
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setShowCreateDialog(false)}>
                      Cancel
                    </Button>
                    <Button type="submit">Create Firm</Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search firms by name, email, or contact..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Firms ({filteredFirms.length})</CardTitle>
            <CardDescription>
              Manage construction firms and their subscription details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Firm Details</TableHead>
                  <TableHead>Contact Info</TableHead>
                  <TableHead>Subscription</TableHead>
                  <TableHead>Limits</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredFirms.map((firm) => (
                  <TableRow key={firm.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <Building2 className="h-8 w-8 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{firm.name}</div>
                          <div className="text-sm text-gray-500">{firm.description}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Phone className="h-3 w-3 mr-1 text-gray-400" />
                          {firm.contact}
                        </div>
                        {firm.email && (
                          <div className="flex items-center text-sm">
                            <Mail className="h-3 w-3 mr-1 text-gray-400" />
                            {firm.email}
                          </div>
                        )}
                        {firm.website && (
                          <div className="flex items-center text-sm">
                            <Globe className="h-3 w-3 mr-1 text-gray-400" />
                            {firm.website}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {getStatusBadge(firm.subscriptionStatus, firm.isActive)}
                        {firm.subscriptionExpiryDate && (
                          <div className="text-xs text-gray-500">
                            Expires: {new Date(firm.subscriptionExpiryDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Users className="h-3 w-3 mr-1 text-gray-400" />
                          {firm.maxUsers} users
                        </div>
                        <div className="flex items-center text-sm">
                          <MapPin className="h-3 w-3 mr-1 text-gray-400" />
                          {firm.maxSites} sites
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {firm.isActive ? (
                        <Badge variant="default">Active</Badge>
                      ) : (
                        <Badge variant="destructive">Inactive</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate(`/superadmin/firms/${firm.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant={firm.isActive ? "destructive" : "default"}
                          size="sm"
                          onClick={() => toggleFirmStatus(firm.id, firm.isActive)}
                        >
                          {firm.isActive ? 'Deactivate' : 'Activate'}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {filteredFirms.length === 0 && (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No firms found matching your search.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SuperAdminFirms;
