
import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useSite } from "@/contexts/SiteContext";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import SiteBasicInfoForm from "@/components/site/SiteBasicInfoForm";
import SiteCostEstimationForm from "@/components/site/SiteCostEstimationForm";
import SiteFileUploadForm from "@/components/site/SiteFileUploadForm";

const CreateSite = () => {
  const navigate = useNavigate();
  const { addSite } = useSite();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    siteName: "",
    location: "",
    area: "",
    areaUnit: "sqft" as "sqft" | "sqm",
    siteType: "residential" as "residential" | "commercial" | "industrial" | "infrastructure",
    estimatedCost: "",
    description: "",
  });
  
  const [drawingFile, setDrawingFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: "Please upload an image (JPEG, PNG, GIF) or PDF file.",
          variant: "destructive",
        });
        return;
      }
      
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please upload a file smaller than 10MB.",
          variant: "destructive",
        });
        return;
      }
      
      setDrawingFile(file);
      toast({
        title: "File uploaded",
        description: `${file.name} has been uploaded successfully.`,
      });
    }
  };

  // Placeholder function for AI estimate generation (coming soon)
  const generateEstimate = () => {
    toast({
      title: "Coming Soon",
      description: "AI estimate generation feature will be available soon.",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.siteName || !formData.location || !formData.area || !formData.estimatedCost) {
      toast({
        title: "Missing required fields",
        description: "Please fill in all required fields including estimated cost.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const siteData = {
        siteName: formData.siteName,
        location: formData.location,
        area: parseFloat(formData.area),
        areaUnit: formData.areaUnit,
        siteType: formData.siteType,
        estimatedCost: parseFloat(formData.estimatedCost),
        drawingFile: drawingFile ? URL.createObjectURL(drawingFile) : undefined,
        progress: 0,
      };

      await addSite(siteData);
      
      toast({
        title: "Site created successfully",
        description: `${formData.siteName} has been added to your projects.`,
      });
      
      navigate("/dashboard");
    } catch (error) {
      toast({
        title: "Error creating site",
        description: "There was an error creating your site. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Link to="/dashboard">
              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div className="text-center sm:text-left">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Create New Site</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base">Add a new construction project to your portfolio</p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6 sm:space-y-8">
          <SiteBasicInfoForm 
            formData={formData}
            onInputChange={handleInputChange}
          />

          <SiteCostEstimationForm 
            formData={formData}
            onInputChange={handleInputChange}
            onGenerateEstimate={generateEstimate}
          />

          <SiteFileUploadForm 
            drawingFile={drawingFile}
            onFileUpload={handleFileUpload}
          />

          {/* Submit Button */}
          <div className="flex flex-col sm:flex-row justify-end space-y-4 sm:space-y-0 sm:space-x-4">
            <Link to="/dashboard" className="w-full sm:w-auto">
              <Button type="button" variant="outline" className="w-full">
                Cancel
              </Button>
            </Link>
            <Button 
              type="submit" 
              className="w-full sm:w-auto bg-black hover:bg-gray-800 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Creating Site..." : "Create Site"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateSite;
