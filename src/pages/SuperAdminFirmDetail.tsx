import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  ArrowLeft, 
  Users, 
  MapPin, 
  UserPlus,
  Eye,
  Power,
  PowerOff,
  Copy,
  Mail
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { superAdminApiClient } from '@/lib/superAdminApi';

interface Firm {
  id: string;
  name: string;
  description?: string;
  address: string;
  contact: string;
  email?: string;
  website?: string;
  isActive: boolean;
  subscriptionStatus: string;
  currentUserCount: number;
  currentSiteCount: number;
  maxUsers: number;
  maxSites: number;
  createdAt: string;
}

interface FirmUser {
  id: string;
  email: string;
  firmName: string;
  address: string;
  contact: string;
  userType: string;
  isActive: boolean;
  isFirstLogin: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

const SuperAdminFirmDetail: React.FC = () => {
  const { firmId } = useParams<{ firmId: string }>();
  const [firm, setFirm] = useState<Firm | null>(null);
  const [users, setUsers] = useState<FirmUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateAdminDialog, setShowCreateAdminDialog] = useState(false);
  const [newAdmin, setNewAdmin] = useState({
    email: '',
    address: '',
    contact: ''
  });
  const [generatedCredentials, setGeneratedCredentials] = useState<any>(null);
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    if (firmId) {
      fetchFirmDetails();
      fetchFirmUsers();
    }
  }, [firmId]);

  const fetchFirmDetails = async () => {
    try {
      const data = await superAdminApiClient.getFirmById(firmId!);
      setFirm(data);
    } catch (error) {
      console.error('Error fetching firm details:', error);
      toast({
        title: "Error",
        description: "Failed to load firm details",
        variant: "destructive",
      });
    }
  };

  const fetchFirmUsers = async () => {
    try {
      const data = await superAdminApiClient.getFirmUsers(firmId!);
      setUsers(data);
    } catch (error) {
      console.error('Error fetching firm users:', error);
      toast({
        title: "Error",
        description: "Failed to load firm users",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAdmin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const credentials = await superAdminApiClient.createFirmAdmin(firmId!, newAdmin);
      setGeneratedCredentials(credentials);
      setNewAdmin({ email: '', address: '', contact: '' });
      fetchFirmUsers();
      
      toast({
        title: "Success",
        description: "Firm admin created successfully",
      });
    } catch (error) {
      console.error('Error creating firm admin:', error);
      toast({
        title: "Error",
        description: "Failed to create firm admin",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Copied to clipboard",
    });
  };

  const getStatusBadge = (status: string, isActive: boolean) => {
    if (!isActive) {
      return <Badge variant="destructive">Inactive</Badge>;
    }
    
    switch (status.toLowerCase()) {
      case 'active':
        return <Badge variant="default">Active</Badge>;
      case 'suspended':
        return <Badge variant="secondary">Suspended</Badge>;
      case 'expired':
        return <Badge variant="outline">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getUserTypeBadge = (userType: string) => {
    switch (userType.toLowerCase()) {
      case 'firmadmin':
        return <Badge variant="default">Admin</Badge>;
      case 'firmuser':
        return <Badge variant="secondary">User</Badge>;
      default:
        return <Badge variant="outline">{userType}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading firm details...</p>
        </div>
      </div>
    );
  }

  if (!firm) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Firm Not Found</h2>
          <p className="text-gray-600 mb-4">The requested firm could not be found.</p>
          <Button onClick={() => navigate('/superadmin/firms')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Firms
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => navigate('/superadmin/firms')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Firms
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{firm.name}</h1>
                <p className="text-sm text-gray-600">Firm Details & User Management</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge(firm.subscriptionStatus, firm.isActive)}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Firm Information */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Firm Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Name</Label>
                  <p className="text-sm">{firm.name}</p>
                </div>
                {firm.description && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Description</Label>
                    <p className="text-sm">{firm.description}</p>
                  </div>
                )}
                <div>
                  <Label className="text-sm font-medium text-gray-500">Address</Label>
                  <p className="text-sm">{firm.address}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Contact</Label>
                  <p className="text-sm">{firm.contact}</p>
                </div>
                {firm.email && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Email</Label>
                    <p className="text-sm">{firm.email}</p>
                  </div>
                )}
                {firm.website && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Website</Label>
                    <p className="text-sm">{firm.website}</p>
                  </div>
                )}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Users</Label>
                    <p className="text-sm">{firm.currentUserCount}/{firm.maxUsers}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Sites</Label>
                    <p className="text-sm">{firm.currentSiteCount}/{firm.maxSites}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Users Management */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Users</CardTitle>
                    <CardDescription>Manage firm users and administrators</CardDescription>
                  </div>
                  <Dialog open={showCreateAdminDialog} onOpenChange={setShowCreateAdminDialog}>
                    <DialogTrigger asChild>
                      <Button>
                        <UserPlus className="h-4 w-4 mr-2" />
                        Add Admin
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create Firm Administrator</DialogTitle>
                        <DialogDescription>
                          Create a new administrator for {firm.name}
                        </DialogDescription>
                      </DialogHeader>
                      <form onSubmit={handleCreateAdmin} className="space-y-4">
                        <div>
                          <Label htmlFor="email">Email *</Label>
                          <Input
                            id="email"
                            type="email"
                            value={newAdmin.email}
                            onChange={(e) => setNewAdmin({ ...newAdmin, email: e.target.value })}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="contact">Contact *</Label>
                          <Input
                            id="contact"
                            value={newAdmin.contact}
                            onChange={(e) => setNewAdmin({ ...newAdmin, contact: e.target.value })}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="address">Address *</Label>
                          <Input
                            id="address"
                            value={newAdmin.address}
                            onChange={(e) => setNewAdmin({ ...newAdmin, address: e.target.value })}
                            required
                          />
                        </div>
                        <div className="flex justify-end space-x-2">
                          <Button type="button" variant="outline" onClick={() => setShowCreateAdminDialog(false)}>
                            Cancel
                          </Button>
                          <Button type="submit">Create Admin</Button>
                        </div>
                      </form>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{getUserTypeBadge(user.userType)}</TableCell>
                        <TableCell>
                          <Badge variant={user.isActive ? "default" : "destructive"}>
                            {user.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never'}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant={user.isActive ? "destructive" : "default"}
                              size="sm"
                            >
                              {user.isActive ? (
                                <PowerOff className="h-4 w-4" />
                              ) : (
                                <Power className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Credentials Dialog */}
        {generatedCredentials && (
          <Dialog open={!!generatedCredentials} onOpenChange={() => setGeneratedCredentials(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Admin Credentials Generated</DialogTitle>
                <DialogDescription>
                  Please save these credentials and share them securely with the firm administrator.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Email</Label>
                  <div className="flex items-center space-x-2">
                    <Input value={generatedCredentials.email} readOnly />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(generatedCredentials.email)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label>Temporary Password</Label>
                  <div className="flex items-center space-x-2">
                    <Input value={generatedCredentials.temporaryPassword} readOnly />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(generatedCredentials.temporaryPassword)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label>Login URL</Label>
                  <div className="flex items-center space-x-2">
                    <Input value={generatedCredentials.loginUrl} readOnly />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(generatedCredentials.loginUrl)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-sm text-yellow-800">
                    <strong>Important:</strong> The temporary password will need to be changed on first login.
                    Please share these credentials securely with the firm administrator.
                  </p>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </main>
    </div>
  );
};

export default SuperAdminFirmDetail;
