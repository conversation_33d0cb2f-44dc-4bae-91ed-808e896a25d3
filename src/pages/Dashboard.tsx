import { useSite } from "@/contexts/SiteContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Building2, Plus, MapPin, TrendingUp, AlertTriangle, CheckCircle, Eye, Wallet } from "lucide-react";
import { Link } from "react-router-dom";
import ReportsSection from "@/components/ReportsSection";

const Dashboard = () => {
  const { sites, materialEntries } = useSite();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Calculate total expenditure across all sites
  const totalExpenditure = materialEntries.reduce((sum, entry) => sum + (entry.price * entry.quantity), 0);

  // Calculate active vs completed sites
  const activeSites = sites.filter(site => (site.progress || 0) < 100).length;
  const completedSites = sites.filter(site => (site.progress || 0) >= 100).length;

  // Get latest 3 sites for display
  const latestSites = sites
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 3);

  // Calculate sites over budget
  const sitesOverBudget = sites.filter(site => {
    if (!site.estimatedCost) return false;
    const siteActualCost = materialEntries
      .filter(entry => entry.siteId === site.id)
      .reduce((sum, entry) => sum + (entry.price * entry.quantity), 0);
    return siteActualCost > site.estimatedCost;
  }).length;

  return (
    <div className="min-h-screen bg-gray-50 overflow-x-hidden">
      <div className="w-full max-w-7xl mx-auto px-4 py-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-bold text-black break-words">Construction Dashboard</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base break-words">Manage your construction projects and track progress</p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
            <Link to="/personal-expenses" className="w-full sm:w-auto">
              <Button variant="outline" className="w-full sm:w-auto border-black text-black hover:bg-black hover:text-white">
                <Wallet className="h-4 w-4 mr-2" />
                Personal Expenses
              </Button>
            </Link>
            <Link to="/create-site" className="w-full sm:w-auto">
              <Button className="w-full sm:w-auto bg-black hover:bg-gray-800 text-white">
                <Plus className="h-4 w-4 mr-2" />
                New Site
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Sites</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            </CardHeader>
            <CardContent>
              <div className="text-xl sm:text-2xl font-bold">{sites.length}</div>
              <p className="text-xs text-muted-foreground break-words">
                {activeSites} active, {completedSites} completed
              </p>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Expenditure</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold break-all">{formatCurrency(totalExpenditure)}</div>
              <p className="text-xs text-muted-foreground">Across all projects</p>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            </CardHeader>
            <CardContent>
              <div className="text-xl sm:text-2xl font-bold">{activeSites}</div>
              <p className="text-xs text-muted-foreground">In progress</p>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Over Budget</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            </CardHeader>
            <CardContent>
              <div className="text-xl sm:text-2xl font-bold">{sitesOverBudget}</div>
              <p className="text-xs text-muted-foreground">Sites exceeding budget</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Sites */}
        <Card className="overflow-hidden">
          <CardHeader>
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div>
                <CardTitle className="text-black text-lg sm:text-xl break-words">Recent Projects</CardTitle>
                <CardDescription className="text-sm sm:text-base">Latest construction sites</CardDescription>
              </div>
              <Link to="/manage-sites" className="w-full sm:w-auto">
                <Button variant="outline" size="sm" className="w-full sm:w-auto">
                  <Eye className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="overflow-x-hidden">
            {latestSites.length === 0 ? (
              <div className="text-center py-12">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4 text-sm sm:text-base">No construction sites created yet</p>
                <Link to="/create-site">
                  <Button className="bg-black hover:bg-gray-800 text-white">
                    Create Your First Site
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {latestSites.map((site) => {
                  const siteActualCost = materialEntries
                    .filter(entry => entry.siteId === site.id)
                    .reduce((sum, entry) => sum + (entry.price * entry.quantity), 0);
                  const isOverBudget = site.estimatedCost && siteActualCost > site.estimatedCost;
                  const progress = site.progress || 0;

                  return (
                    <div key={site.id} className="flex flex-col space-y-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors lg:flex-row lg:items-center lg:justify-between lg:space-y-0 overflow-x-hidden">
                      <div className="flex flex-col space-y-4 flex-1 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4 min-w-0">
                        <div className="bg-gray-100 p-3 rounded-lg w-fit mx-auto sm:mx-0 flex-shrink-0">
                          <Building2 className="h-6 w-6 text-black" />
                        </div>
                        <div className="flex-1 text-center sm:text-left min-w-0">
                          <h3 className="font-semibold text-lg break-words">{site.siteName}</h3>
                          <div className="flex items-center justify-center sm:justify-start text-sm text-gray-500 mt-1 min-w-0">
                            <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                            <span className="break-words truncate">{site.location}</span>
                          </div>
                          <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2 mt-2">
                            <Badge variant={progress >= 100 ? "default" : isOverBudget ? "destructive" : "secondary"} className="text-xs flex-shrink-0">
                              {progress >= 100 ? "Completed" : isOverBudget ? "Over Budget" : "In Progress"}
                            </Badge>
                            <span className="text-xs text-gray-500 break-words">
                              {site.area} {site.areaUnit}
                            </span>
                            {siteActualCost > 0 && (
                              <span className="text-xs text-gray-500 break-all">
                                Spent: {formatCurrency(siteActualCost)}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-center space-y-4 flex-shrink-0 sm:flex-row sm:space-y-0 sm:space-x-4">
                        <div className="text-center flex-shrink-0">
                          <div className="text-lg font-semibold">{progress}%</div>
                          <Progress value={progress} className="w-24 h-2" />
                        </div>
                        <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                          <Link to={`/site/${site.id}`}>View Details</Link>
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Reports Section */}
        <div className="overflow-x-hidden">
          <ReportsSection />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
