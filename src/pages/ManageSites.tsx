
import { useState } from "react";
import { Link } from "react-router-dom";
import { useSite } from "@/contexts/SiteContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { ArrowLeft, Building2, MapPin, Edit, Trash2, AlertTriangle, CheckCircle, Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import SiteSearchAndFilter from "@/components/site/SiteSearchAndFilter";

const ManageSites = () => {
  const { sites, materialEntries, deleteSite } = useSite();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<'all' | 'active' | 'completed'>('all');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleDeleteSite = async (siteId: string, siteName: string) => {
    try {
      await deleteSite(siteId);
      toast({
        title: "Site Deleted",
        description: `"${siteName}" has been successfully deleted.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete site. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Filter and search sites
  const filteredSites = sites.filter(site => {
    const progress = site.progress || 0;
    const matchesSearch = site.siteName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         site.location.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesFilter = true;
    if (filterType === 'active') matchesFilter = progress < 100;
    if (filterType === 'completed') matchesFilter = progress >= 100;
    
    return matchesSearch && matchesFilter;
  });

  // Get site status
  const getSiteStatus = (site: any) => {
    const progress = site.progress || 0;
    const siteActualCost = materialEntries
      .filter(entry => entry.siteId === site.id)
      .reduce((sum, entry) => sum + (entry.price * entry.quantity), 0);
    const isOverBudget = site.estimatedCost && siteActualCost > site.estimatedCost;
    
    if (progress >= 100) return { status: 'completed', color: 'bg-gray-100 text-gray-800', icon: CheckCircle };
    if (isOverBudget) return { status: 'over-budget', color: 'bg-black text-white', icon: AlertTriangle };
    if (progress > 0) return { status: 'in-progress', color: 'bg-gray-200 text-gray-900', icon: Clock };
    return { status: 'planning', color: 'bg-gray-100 text-gray-800', icon: Building2 };
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      <div className="max-w-6xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Link to="/dashboard">
                <Button variant="outline" size="sm" className="w-full sm:w-auto">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div className="text-center sm:text-left">
                <h1 className="text-2xl sm:text-3xl font-bold text-black">Manage Sites</h1>
                <p className="text-gray-600 mt-1 text-sm sm:text-base">View, edit, and delete your construction sites</p>
              </div>
            </div>
            <Badge variant="outline" className="text-sm border-gray-300 text-gray-700 w-fit mx-auto lg:mx-0">
              {filteredSites.length} of {sites.length} sites
            </Badge>
          </div>
        </div>

        <SiteSearchAndFilter 
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filterType={filterType}
          setFilterType={setFilterType}
        />

        {/* Sites List */}
        <Card>
          <CardHeader>
            <CardTitle className="text-black text-lg sm:text-xl">All Sites ({filteredSites.length})</CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Manage your construction projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredSites.length === 0 ? (
              <div className="text-center py-12">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4 text-sm sm:text-base">
                  {searchTerm || filterType !== 'all' ? 'No sites match your criteria' : 'No sites created yet'}
                </p>
                <Link to="/create-site">
                  <Button className="bg-black hover:bg-gray-800 text-white">
                    Create Your First Site
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredSites.map((site) => {
                  const { status, color, icon: StatusIcon } = getSiteStatus(site);
                  const siteActualCost = materialEntries
                    .filter(entry => entry.siteId === site.id)
                    .reduce((sum, entry) => sum + (entry.price * entry.quantity), 0);
                  
                  return (
                    <div key={site.id} className="flex flex-col lg:flex-row lg:items-center lg:justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors space-y-4 lg:space-y-0">
                      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 flex-1">
                        <div className="bg-gray-100 p-3 rounded-lg w-fit mx-auto sm:mx-0">
                          <Building2 className="h-6 w-6 text-black" />
                        </div>
                        <div className="flex-1 text-center sm:text-left">
                          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <div className="space-y-2">
                              <h3 className="font-semibold text-lg break-words">{site.siteName}</h3>
                              <div className="flex items-center justify-center sm:justify-start text-sm text-gray-500">
                                <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                                <span className="break-words">{site.location} • {site.area} {site.areaUnit}</span>
                              </div>
                              <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2 sm:gap-4">
                                <Badge className={`text-xs ${color} border-0`}>
                                  <StatusIcon className="h-3 w-3 mr-1" />
                                  {status.replace('-', ' ')}
                                </Badge>
                                {site.estimatedCost && (
                                  <span className="text-xs text-gray-500 break-words">
                                    Budget: {formatCurrency(site.estimatedCost)}
                                  </span>
                                )}
                                {siteActualCost > 0 && (
                                  <span className="text-xs text-gray-500 break-words">
                                    Spent: {formatCurrency(siteActualCost)}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="text-center mt-4 lg:mt-0 lg:text-right">
                              <div className="text-lg font-semibold mb-1">
                                {site.progress || 0}%
                              </div>
                              <Progress value={site.progress || 0} className="w-24 h-2 mx-auto lg:mx-0" />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto lg:ml-4">
                        <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                          <Link to={`/site/${site.id}`}>
                            View
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                          <Link to={`/edit-site/${site.id}`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm" className="w-full sm:w-auto text-red-600 hover:text-red-700 hover:bg-red-50">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="mx-4 max-w-lg">
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Site</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete "{site.siteName}"? This action cannot be undone and will remove all associated materials and progress entries.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter className="flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                              <AlertDialogCancel className="w-full sm:w-auto">Cancel</AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => handleDeleteSite(site.id, site.siteName)}
                                className="w-full sm:w-auto bg-red-600 hover:bg-red-700"
                              >
                                Delete Site
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ManageSites;
