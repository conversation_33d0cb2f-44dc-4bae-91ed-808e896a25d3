
import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, Link, useNavigate } from "react-router-dom";
import { useSite } from "@/contexts/SiteContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { ArrowLeft, MapPin, Edit, Trash2 } from "lucide-react";
import MaterialTracker from "@/components/MaterialTracker";
import ProgressTracker from "@/components/ProgressTracker";
import { useToast } from "@/hooks/use-toast";
import SiteOverviewCards from "@/components/site/SiteOverviewCards";
import SiteEditDialog from "@/components/site/SiteEditDialog";

const SiteDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getSiteById, getMaterialEntriesBySite, getProgressEntriesBySite, deleteSite } = useSite();
  const { toast } = useToast();

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [materialEntries, setMaterialEntries] = useState<any[]>([]);
  const [progressEntries, setProgressEntries] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const site = getSiteById(id!);

  useEffect(() => {
    const loadSiteData = async () => {
      if (!id || isLoading) return;

      try {
        setIsLoading(true);
        const [materials, progress] = await Promise.all([
          getMaterialEntriesBySite(id),
          getProgressEntriesBySite(id)
        ]);
        setMaterialEntries(materials);
        setProgressEntries(progress);
      } catch (error) {
        console.error('Failed to load site data:', error);
        toast({
          title: "Error",
          description: "Failed to load site data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSiteData();
  }, [id]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleDeleteSite = async () => {
    try {
      await deleteSite(id!);
      toast({
        title: "Site Deleted",
        description: "The site has been successfully deleted.",
      });
      navigate("/dashboard");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete site. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!site) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
        <div className="text-center py-12">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Site Not Found</h1>
          <p className="text-gray-600 mt-2 text-sm sm:text-base">The requested site could not be found.</p>
          <Link to="/dashboard">
            <Button className="mt-4">Return to Dashboard</Button>
          </Link>
        </div>
      </div>
    );
  }

  const totalMaterialCost = materialEntries.reduce((sum, entry) => sum + (entry.price * entry.quantity), 0);
  const estimatedVsActual = site.estimatedCost ? ((site.estimatedCost - totalMaterialCost) / site.estimatedCost * 100) : 0;

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      <div className="max-w-6xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Link to="/dashboard">
                <Button variant="outline" size="sm" className="w-full sm:w-auto">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              </Link>
              <div className="text-center sm:text-left">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">{site.siteName}</h1>
                <div className="flex items-center justify-center sm:justify-start text-gray-600 mt-1">
                  <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                  <span className="text-sm sm:text-base break-words">{site.location}</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="text-center sm:text-right">
                <div className="text-sm text-gray-500">Created</div>
                <div className="font-medium text-sm sm:text-base">
                  {new Date(site.createdAt).toLocaleDateString()}
                </div>
              </div>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setIsEditDialogOpen(true)}
                  className="w-full sm:w-auto"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm" className="w-full sm:w-auto text-red-600 hover:text-red-700 hover:bg-red-50">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="mx-4 max-w-lg">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Site</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete "{site.siteName}"? This action cannot be undone and will remove all associated materials and progress entries.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                      <AlertDialogCancel className="w-full sm:w-auto">Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDeleteSite} className="w-full sm:w-auto bg-red-600 hover:bg-red-700">
                        Delete Site
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </div>
        </div>

        {/* Site Overview */}
        <SiteOverviewCards 
          site={site}
          totalMaterialCost={totalMaterialCost}
          estimatedVsActual={estimatedVsActual}
          formatCurrency={formatCurrency}
        />

        {/* Site Drawing */}
        {site.drawingFile && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Site Drawing/Plan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg overflow-hidden">
                {typeof site.drawingFile === 'string' && site.drawingFile.endsWith('.pdf') ? (
                  <div className="p-6 sm:p-8 text-center bg-gray-50">
                    <div className="text-gray-600">PDF file uploaded</div>
                    <Button variant="outline" className="mt-2" asChild>
                      <a href={typeof site.drawingFile === 'string' ? site.drawingFile : URL.createObjectURL(site.drawingFile)} target="_blank" rel="noopener noreferrer">
                        View PDF
                      </a>
                    </Button>
                  </div>
                ) : (
                  <img 
                    src={typeof site.drawingFile === 'string' ? site.drawingFile : URL.createObjectURL(site.drawingFile)} 
                    alt="Site drawing" 
                    className="w-full h-auto max-h-64 sm:max-h-96 object-contain"
                  />
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs defaultValue="materials" className="space-y-4 sm:space-y-6">
          <TabsList className="grid w-full grid-cols-2 h-auto">
            <TabsTrigger value="materials" className="text-xs sm:text-sm py-2 sm:py-3">
              Material Tracking
            </TabsTrigger>
            <TabsTrigger value="progress" className="text-xs sm:text-sm py-2 sm:py-3">
              Progress Tracking
            </TabsTrigger>
          </TabsList>

          <TabsContent value="materials">
            <MaterialTracker siteId={site.id} />
          </TabsContent>

          <TabsContent value="progress">
            <ProgressTracker siteId={site.id} />
          </TabsContent>
        </Tabs>

        {/* Edit Dialog */}
        <SiteEditDialog 
          site={site}
          isOpen={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
        />
      </div>
    </div>
  );
};

export default SiteDetails;
