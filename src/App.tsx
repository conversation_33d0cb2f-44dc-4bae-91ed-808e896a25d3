
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { SuperAdminProvider } from "@/contexts/SuperAdminContext";
import { SiteProvider } from "@/contexts/SiteContext";
import { PersonalExpenseProvider } from "@/contexts/PersonalExpenseContext";
import LoginPage from "@/pages/LoginPage";
import Dashboard from "@/pages/Dashboard";
import SiteDetails from "@/pages/SiteDetails";
import CreateSite from "@/pages/CreateSite";
import ManageSites from "@/pages/ManageSites";
import PersonalExpenses from "@/pages/PersonalExpenses";
import SuperAdminLogin from "@/pages/SuperAdminLogin";
import SuperAdminDashboard from "@/pages/SuperAdminDashboard";
import SuperAdminFirms from "@/pages/SuperAdminFirms";
import SuperAdminFirmDetail from "@/pages/SuperAdminFirmDetail";
import Navigation from "@/components/Navigation";
import PasswordChangeDialog from "@/components/PasswordChangeDialog";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

function AppContent() {
  const { user } = useAuth();

  if (!user) {
    return <LoginPage />;
  }

  return (
    <>
      <Navigation />
      <PasswordChangeDialog />
      <main className="container mx-auto px-4 py-8">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/create-site" element={<CreateSite />} />
          <Route path="/manage-sites" element={<ManageSites />} />
          <Route path="/site/:id" element={<SiteDetails />} />
          <Route path="/personal-expenses" element={<PersonalExpenses />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </main>
    </>
  );
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <SuperAdminProvider>
          <AuthProvider>
            <SiteProvider>
              <PersonalExpenseProvider>
                <div className="min-h-screen bg-gray-50">
                  <Routes>
                    {/* SuperAdmin routes */}
                    <Route path="/superadmin/login" element={<SuperAdminLogin />} />
                    <Route path="/superadmin/dashboard" element={<SuperAdminDashboard />} />
                    <Route path="/superadmin/firms" element={<SuperAdminFirms />} />
                    <Route path="/superadmin/firms/:firmId" element={<SuperAdminFirmDetail />} />

                    {/* Regular user routes */}
                    <Route path="/*" element={<AppContent />} />

                    {/* Catch-all for unknown routes */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </div>
              </PersonalExpenseProvider>
            </SiteProvider>
          </AuthProvider>
        </SuperAdminProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
