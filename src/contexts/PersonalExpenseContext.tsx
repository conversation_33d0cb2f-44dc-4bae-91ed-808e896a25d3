
import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';

export interface PersonalExpense {
  id: string;
  description: string;
  amount: number;
  category: string;
  date: string;
  createdAt: string;
}

interface PersonalExpenseContextType {
  expenses: PersonalExpense[];
  addExpense: (expense: Omit<PersonalExpense, 'id' | 'createdAt'>) => Promise<void>;
  updateExpense: (id: string, expense: Partial<PersonalExpense>) => Promise<void>;
  deleteExpense: (id: string) => Promise<void>;
  getTotalExpenses: () => number;
  getExpensesByCategory: () => Record<string, number>;
  getExpensesByMonth: () => Record<string, number>;
}

const PersonalExpenseContext = createContext<PersonalExpenseContextType | undefined>(undefined);

export const PersonalExpenseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [expenses, setExpenses] = useState<PersonalExpense[]>([]);

  // Load expenses from API on mount
  useEffect(() => {
    const loadExpenses = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        if (!token) return;

        const expensesData = await apiClient.getPersonalExpenses();
        setExpenses(expensesData);
      } catch (error) {
        console.error('Failed to load expenses:', error);
      }
    };

    loadExpenses();
  }, []);

  const addExpense = async (expenseData: Omit<PersonalExpense, 'id' | 'createdAt'>) => {
    try {
      const newExpense = await apiClient.createPersonalExpense(expenseData);
      setExpenses(prev => [newExpense, ...prev]);
    } catch (error) {
      console.error('Failed to create expense:', error);
      throw error;
    }
  };

  const updateExpense = async (id: string, updates: Partial<PersonalExpense>) => {
    try {
      const updatedExpense = await apiClient.updatePersonalExpense(id, updates);
      setExpenses(prev => prev.map(expense =>
        expense.id === id ? updatedExpense : expense
      ));
    } catch (error) {
      console.error('Failed to update expense:', error);
      throw error;
    }
  };

  const deleteExpense = async (id: string) => {
    try {
      await apiClient.deletePersonalExpense(id);
      setExpenses(prev => prev.filter(expense => expense.id !== id));
    } catch (error) {
      console.error('Failed to delete expense:', error);
      throw error;
    }
  };

  const getTotalExpenses = () => {
    return expenses.reduce((total, expense) => total + expense.amount, 0);
  };

  const getExpensesByCategory = () => {
    return expenses.reduce((acc, expense) => {
      acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
      return acc;
    }, {} as Record<string, number>);
  };

  const getExpensesByMonth = () => {
    return expenses.reduce((acc, expense) => {
      const month = new Date(expense.date).toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short' 
      });
      acc[month] = (acc[month] || 0) + expense.amount;
      return acc;
    }, {} as Record<string, number>);
  };

  return (
    <PersonalExpenseContext.Provider value={{
      expenses,
      addExpense,
      updateExpense,
      deleteExpense,
      getTotalExpenses,
      getExpensesByCategory,
      getExpensesByMonth,
    }}>
      {children}
    </PersonalExpenseContext.Provider>
  );
};

export const usePersonalExpense = () => {
  const context = useContext(PersonalExpenseContext);
  if (context === undefined) {
    throw new Error('usePersonalExpense must be used within a PersonalExpenseProvider');
  }
  return context;
};
