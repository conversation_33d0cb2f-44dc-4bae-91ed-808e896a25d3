import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';

interface User {
  id: string;
  email: string;
  firmId: string;
  firmName: string;
  firmLogo?: string;
  address: string;
  contact: string;
  userType: string;
  isFirstLogin?: boolean;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: Omit<User, 'id'> & { password: string }) => Promise<boolean>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => void;
  changePassword: (currentPassword: string, newPassword: string) => Promise<boolean>;
  showPasswordChangeDialog: boolean;
  setShowPasswordChangeDialog: (show: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [showPasswordChangeDialog, setShowPasswordChangeDialog] = useState(false);

  useEffect(() => {
    // Check for stored user data on app load
    const storedUser = localStorage.getItem('constructionApp_user');
    const token = localStorage.getItem('auth_token');

    if (storedUser && token) {
      const userData = JSON.parse(storedUser);
      setUser(userData);
    }
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await apiClient.login(email, password);

      // Store the token
      localStorage.setItem('auth_token', response.token);

      // Store user data
      setUser(response.user);
      localStorage.setItem('constructionApp_user', JSON.stringify(response.user));

      return true;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const register = async (userData: Omit<User, 'id'> & { password: string }): Promise<boolean> => {
    // Registration is now handled by SuperAdmins creating firms and firm admins
    // This function is kept for backward compatibility but should not be used
    console.warn('Public registration is disabled. Firms must be created by SuperAdmins.');
    return false;
  };

  const changePassword = async (currentPassword: string, newPassword: string): Promise<boolean> => {
    try {
      if (!user) return false;

      await apiClient.changePassword(currentPassword, newPassword);

      // Update current user to remove first login flag
      const updatedUser = { ...user, isFirstLogin: false };
      setUser(updatedUser);
      localStorage.setItem('constructionApp_user', JSON.stringify(updatedUser));

      setShowPasswordChangeDialog(false);
      return true;
    } catch (error) {
      console.error('Password change error:', error);
      return false;
    }
  };

  const logout = () => {
    setUser(null);
    setShowPasswordChangeDialog(false);
    localStorage.removeItem('constructionApp_user');
    localStorage.removeItem('auth_token');
  };

  const updateProfile = async (userData: Partial<User>) => {
    if (user) {
      try {
        const updatedUser = await apiClient.updateProfile(userData);
        setUser(updatedUser);
        localStorage.setItem('constructionApp_user', JSON.stringify(updatedUser));
      } catch (error) {
        console.error('Profile update failed:', error);
      }
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      login, 
      register, 
      logout, 
      updateProfile, 
      changePassword, 
      showPasswordChangeDialog, 
      setShowPasswordChangeDialog 
    }}>
      {children}
    </AuthContext.Provider>
  );
};
