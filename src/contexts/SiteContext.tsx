
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/lib/api';

export interface Site {
  id: string;
  siteName: string;
  location: string;
  area: number;
  areaUnit: 'sqft' | 'sqm';
  siteType: 'residential' | 'commercial' | 'industrial' | 'infrastructure';
  drawingFile?: File | string;
  createdAt: string;
  estimatedCost?: number;
  materialsRequired?: Material[];
  actualCost?: number;
  progress?: number;
}

export interface Material {
  id: string;
  name: string;
  estimatedQuantity: number;
  unit: string;
  estimatedPrice: number;
  actualQuantity?: number;
  actualPrice?: number;
}

export interface MaterialEntry {
  id: string;
  siteId: string;
  materialName: string;
  quantity: number;
  price: number;
  purchaseDate: string;
  notes?: string;
}

export interface ProgressEntry {
  id: string;
  siteId: string;
  date: string;
  photos: string[];
  notes: string;
}

interface SiteContextType {
  sites: Site[];
  materialEntries: MaterialEntry[];
  progressEntries: ProgressEntry[];
  addSite: (site: Omit<Site, 'id' | 'createdAt'>) => Promise<void>;
  updateSite: (id: string, site: Partial<Site>) => Promise<void>;
  deleteSite: (id: string) => Promise<void>;
  addMaterialEntry: (entry: Omit<MaterialEntry, 'id'>) => Promise<void>;
  addProgressEntry: (entry: Omit<ProgressEntry, 'id'>) => Promise<void>;
  getSiteById: (id: string) => Site | undefined;
  getMaterialEntriesBySite: (siteId: string) => Promise<MaterialEntry[]>;
  getProgressEntriesBySite: (siteId: string) => Promise<ProgressEntry[]>;
}

const SiteContext = createContext<SiteContextType | undefined>(undefined);

export const useSite = () => {
  const context = useContext(SiteContext);
  if (context === undefined) {
    throw new Error('useSite must be used within a SiteProvider');
  }
  return context;
};

export const SiteProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sites, setSites] = useState<Site[]>([]);
  const [materialEntries, setMaterialEntries] = useState<MaterialEntry[]>([]);
  const [progressEntries, setProgressEntries] = useState<ProgressEntry[]>([]);

  useEffect(() => {
    // Load data from API on mount
    const loadData = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        if (!token) return;

        const [sitesData, materialData, progressData] = await Promise.all([
          apiClient.getSites(),
          // We'll load material and progress entries when needed per site
          Promise.resolve([]),
          Promise.resolve([])
        ]);

        setSites(sitesData);
        setMaterialEntries(materialData);
        setProgressEntries(progressData);
      } catch (error) {
        console.error('Failed to load data:', error);
      }
    };

    loadData();
  }, []);

  // Remove localStorage sync effects since we're using API

  const addSite = async (siteData: Omit<Site, 'id' | 'createdAt'>) => {
    try {
      const newSite = await apiClient.createSite({
        siteName: siteData.siteName,
        location: siteData.location,
        area: siteData.area,
        areaUnit: siteData.areaUnit,
        siteType: siteData.siteType,
        drawingFile: typeof siteData.drawingFile === 'string' ? siteData.drawingFile : undefined,
        estimatedCost: siteData.estimatedCost
      });
      setSites(prev => [...prev, newSite]);
    } catch (error) {
      console.error('Failed to create site:', error);
      throw error;
    }
  };

  const updateSite = async (id: string, siteData: Partial<Site>) => {
    try {
      const updatedSite = await apiClient.updateSite(id, siteData);
      setSites(prev => prev.map(site =>
        site.id === id ? updatedSite : site
      ));
    } catch (error) {
      console.error('Failed to update site:', error);
      throw error;
    }
  };

  const deleteSite = async (id: string) => {
    try {
      await apiClient.deleteSite(id);
      setSites(prev => prev.filter(site => site.id !== id));
      setMaterialEntries(prev => prev.filter(entry => entry.siteId !== id));
      setProgressEntries(prev => prev.filter(entry => entry.siteId !== id));
    } catch (error) {
      console.error('Failed to delete site:', error);
      throw error;
    }
  };

  const addMaterialEntry = async (entryData: Omit<MaterialEntry, 'id'>) => {
    try {
      const newEntry = await apiClient.createMaterialEntry(entryData);
      setMaterialEntries(prev => [...prev, newEntry]);
    } catch (error) {
      console.error('Failed to create material entry:', error);
      throw error;
    }
  };

  const addProgressEntry = async (entryData: Omit<ProgressEntry, 'id'>) => {
    try {
      const newEntry = await apiClient.createProgressEntry(entryData);
      setProgressEntries(prev => [...prev, newEntry]);
    } catch (error) {
      console.error('Failed to create progress entry:', error);
      throw error;
    }
  };

  const getSiteById = (id: string) => sites.find(site => site.id === id);

  const getMaterialEntriesBySite = useCallback(async (siteId: string): Promise<MaterialEntry[]> => {
    if (!siteId) {
      console.error('Site ID is required for getMaterialEntriesBySite');
      return [];
    }

    try {
      const entries = await apiClient.getMaterialEntries(siteId);
      // Ensure entries is always an array
      const validEntries = Array.isArray(entries) ? entries : [];

      // Update local state with fresh data
      setMaterialEntries(prev => {
        const filtered = prev.filter(entry => entry.siteId !== siteId);
        return [...filtered, ...validEntries];
      });

      return validEntries;
    } catch (error) {
      console.error('SiteContext: Failed to load material entries:', error);
      // Return cached entries for this site, or empty array if none exist
      const cachedEntries = materialEntries.filter(entry => entry.siteId === siteId);
      return Array.isArray(cachedEntries) ? cachedEntries : [];
    }
  }, []);

  const getProgressEntriesBySite = useCallback(async (siteId: string): Promise<ProgressEntry[]> => {
    if (!siteId) {
      console.error('Site ID is required for getProgressEntriesBySite');
      return [];
    }

    try {
      const entries = await apiClient.getProgressEntries(siteId);
      // Ensure entries is always an array
      const validEntries = Array.isArray(entries) ? entries : [];

      // Update local state with fresh data
      setProgressEntries(prev => {
        const filtered = prev.filter(entry => entry.siteId !== siteId);
        return [...filtered, ...validEntries];
      });

      return validEntries;
    } catch (error) {
      console.error('SiteContext: Failed to load progress entries:', error);
      // Return cached entries for this site, or empty array if none exist
      const cachedEntries = progressEntries.filter(entry => entry.siteId === siteId);
      return Array.isArray(cachedEntries) ? cachedEntries : [];
    }
  }, []);

  return (
    <SiteContext.Provider value={{
      sites,
      materialEntries,
      progressEntries,
      addSite,
      updateSite,
      deleteSite,
      addMaterialEntry,
      addProgressEntry,
      getSiteById,
      getMaterialEntriesBySite,
      getProgressEntriesBySite,
    }}>
      {children}
    </SiteContext.Provider>
  );
};
