
import { Input } from "@/components/ui/input";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, Filter } from "lucide-react";

interface SiteSearchAndFilterProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterType: 'all' | 'active' | 'completed';
  setFilterType: (type: 'all' | 'active' | 'completed') => void;
}

const SiteSearchAndFilter = ({ searchTerm, setSearchTerm, filterType, setFilterType }: SiteSearchAndFilterProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-black text-lg sm:text-xl">Search & Filter</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search sites by name or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select 
              value={filterType} 
              onChange={(e) => setFilterType(e.target.value as any)}
              className="text-sm border rounded-md px-3 py-2 focus:ring-2 focus:ring-black focus:border-black w-full sm:w-auto"
            >
              <option value="all">All Sites</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SiteSearchAndFilter;
