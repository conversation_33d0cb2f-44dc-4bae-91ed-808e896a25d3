
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Calculator } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface SiteCostEstimationFormProps {
  formData: {
    area: string;
    areaUnit: "sqft" | "sqm";
    siteType: "residential" | "commercial" | "industrial" | "infrastructure";
    estimatedCost: string;
  };
  onInputChange: (field: string, value: string) => void;
  onGenerateEstimate: () => void;
}

const SiteCostEstimationForm = ({ formData, onInputChange, onGenerateEstimate }: SiteCostEstimationFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-black text-lg sm:text-xl">
          <Calculator className="h-5 w-5 mr-2" />
          Cost Estimation
        </CardTitle>
        <CardDescription className="text-sm sm:text-base">
          Enter your project cost estimate manually or generate using AI (coming soon)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 sm:space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="estimatedCost" className="text-sm sm:text-base">Manual Estimated Cost (₹) *</Label>
            <Input
              id="estimatedCost"
              type="number"
              placeholder="e.g., 5000000"
              value={formData.estimatedCost}
              onChange={(e) => onInputChange("estimatedCost", e.target.value)}
              className="w-full"
              required
            />
            <p className="text-xs text-gray-500 mt-1">Enter your estimated project cost in Indian Rupees</p>
          </div>
          
          <div className="relative">
            <div className="absolute inset-0 bg-gray-100 bg-opacity-75 rounded-lg flex items-center justify-center z-10">
              <div className="bg-white px-4 py-2 rounded-md shadow-sm border">
                <span className="text-sm font-medium text-gray-600">Coming Soon</span>
              </div>
            </div>
            <div className="opacity-50">
              <Label className="text-sm sm:text-base text-gray-400">AI Estimate Generation</Label>
              <div className="flex flex-col sm:flex-row sm:items-end space-y-4 sm:space-y-0 sm:space-x-4 mt-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  disabled
                  className="w-full sm:w-auto border-gray-300 cursor-not-allowed"
                >
                  <Calculator className="h-4 w-4 mr-2" />
                  Generate AI Estimate
                </Button>
              </div>
            </div>
          </div>
        </div>
        
        {formData.estimatedCost && (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-sm text-gray-800">
              <strong>Estimated Cost:</strong> ₹{parseFloat(formData.estimatedCost).toLocaleString()}
              <br />
              <span className="text-xs">
                Based on {formData.area} {formData.areaUnit} {formData.siteType} project
              </span>
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SiteCostEstimationForm;
