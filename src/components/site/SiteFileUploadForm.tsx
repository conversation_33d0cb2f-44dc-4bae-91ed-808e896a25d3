
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, FileImage, FileText } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface SiteFileUploadFormProps {
  drawingFile: File | null;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const SiteFileUploadForm = ({ drawingFile, onFileUpload }: SiteFileUploadFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-black text-lg sm:text-xl">
          <Upload className="h-5 w-5 mr-2" />
          Site Drawing/Plan
        </CardTitle>
        <CardDescription className="text-sm sm:text-base">
          Upload site plans, blueprints, or reference images (Max 10MB)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 sm:p-8 text-center hover:border-gray-400 transition-colors">
          <input
            type="file"
            id="drawingFile"
            accept="image/*,.pdf"
            onChange={onFileUpload}
            className="hidden"
          />
          <label htmlFor="drawingFile" className="cursor-pointer">
            {drawingFile ? (
              <div className="space-y-2">
                <div className="flex items-center justify-center">
                  {drawingFile.type.startsWith('image/') ? (
                    <FileImage className="h-8 w-8 sm:h-12 sm:w-12 text-green-600" />
                  ) : (
                    <FileText className="h-8 w-8 sm:h-12 sm:w-12 text-red-600" />
                  )}
                </div>
                <p className="text-sm font-medium break-all px-2">{drawingFile.name}</p>
                <p className="text-xs text-gray-500">
                  {(drawingFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
                <Button type="button" variant="outline" size="sm" className="w-full sm:w-auto">
                  Change File
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto" />
                <p className="text-sm font-medium">Click to upload drawing or plan</p>
                <p className="text-xs text-gray-500 px-2">
                  Supports JPEG, PNG, GIF, PDF (Max 10MB)
                </p>
              </div>
            )}
          </label>
        </div>
      </CardContent>
    </Card>
  );
};

export default SiteFileUploadForm;
