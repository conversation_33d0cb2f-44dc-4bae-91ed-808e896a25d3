
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Building2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface SiteBasicInfoFormProps {
  formData: {
    siteName: string;
    location: string;
    area: string;
    areaUnit: "sqft" | "sqm";
    siteType: "residential" | "commercial" | "industrial" | "infrastructure";
    description: string;
  };
  onInputChange: (field: string, value: string) => void;
}

const SiteBasicInfoForm = ({ formData, onInputChange }: SiteBasicInfoFormProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-black text-lg sm:text-xl">
          <Building2 className="h-5 w-5 mr-2" />
          Basic Information
        </CardTitle>
        <CardDescription className="text-sm sm:text-base">
          Enter the basic details about your construction site
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 sm:space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          <div className="space-y-2">
            <Label htmlFor="siteName" className="text-sm sm:text-base">Site Name *</Label>
            <Input
              id="siteName"
              placeholder="e.g., Downtown Office Complex"
              value={formData.siteName}
              onChange={(e) => onInputChange("siteName", e.target.value)}
              required
              className="w-full"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="location" className="text-sm sm:text-base">Location *</Label>
            <Input
              id="location"
              placeholder="e.g., 123 Main St, City, State"
              value={formData.location}
              onChange={(e) => onInputChange("location", e.target.value)}
              required
              className="w-full"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <div className="space-y-2">
            <Label htmlFor="area" className="text-sm sm:text-base">Area *</Label>
            <Input
              id="area"
              type="number"
              placeholder="e.g., 5000"
              value={formData.area}
              onChange={(e) => onInputChange("area", e.target.value)}
              required
              className="w-full"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="areaUnit" className="text-sm sm:text-base">Unit</Label>
            <Select value={formData.areaUnit} onValueChange={(value) => onInputChange("areaUnit", value)}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sqft">Square Feet</SelectItem>
                <SelectItem value="sqm">Square Meters</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="siteType" className="text-sm sm:text-base">Site Type</Label>
            <Select value={formData.siteType} onValueChange={(value) => onInputChange("siteType", value)}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="residential">Residential</SelectItem>
                <SelectItem value="commercial">Commercial</SelectItem>
                <SelectItem value="industrial">Industrial</SelectItem>
                <SelectItem value="infrastructure">Infrastructure</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description" className="text-sm sm:text-base">Description (Optional)</Label>
          <Textarea
            id="description"
            placeholder="Brief description of the project..."
            value={formData.description}
            onChange={(e) => onInputChange("description", e.target.value)}
            rows={3}
            className="w-full resize-none"
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default SiteBasicInfoForm;
