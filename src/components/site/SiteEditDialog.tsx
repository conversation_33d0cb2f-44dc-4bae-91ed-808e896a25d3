
import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useSite } from "@/contexts/SiteContext";
import { useToast } from "@/hooks/use-toast";

interface Site {
  id: string;
  siteName: string;
  location: string;
  area: number;
  areaUnit: "sqft" | "sqm";
  siteType: "residential" | "commercial" | "industrial" | "infrastructure";
  description?: string;
  estimatedCost?: number;
}

interface SiteEditDialogProps {
  site: Site;
  isOpen: boolean;
  onClose: () => void;
}

const SiteEditDialog = ({ site, isOpen, onClose }: SiteEditDialogProps) => {
  const { updateSite } = useSite();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    siteName: site.siteName,
    location: site.location,
    area: site.area.toString(),
    areaUnit: site.areaUnit,
    siteType: site.siteType,
    description: site.description || "",
    estimatedCost: site.estimatedCost?.toString() || "",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.siteName.trim() || !formData.location.trim() || !formData.area.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      const updatedSite = {
        siteName: formData.siteName.trim(),
        location: formData.location.trim(),
        area: parseFloat(formData.area),
        areaUnit: formData.areaUnit as "sqft" | "sqm",
        siteType: formData.siteType as "residential" | "commercial" | "industrial" | "infrastructure",
        estimatedCost: formData.estimatedCost ? parseFloat(formData.estimatedCost) : undefined,
      };

      await updateSite(site.id, updatedSite);

      toast({
        title: "Site Updated",
        description: "Site details have been successfully updated.",
      });

      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update site. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Site Details</DialogTitle>
          <DialogDescription>
            Update the basic information for your construction site.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="siteName">Site Name *</Label>
              <Input
                id="siteName"
                value={formData.siteName}
                onChange={(e) => handleInputChange("siteName", e.target.value)}
                placeholder="e.g., Downtown Office Complex"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="location">Location *</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                placeholder="e.g., 123 Main St, City, State"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="area">Area *</Label>
              <Input
                id="area"
                type="number"
                value={formData.area}
                onChange={(e) => handleInputChange("area", e.target.value)}
                placeholder="e.g., 5000"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="areaUnit">Unit</Label>
              <Select value={formData.areaUnit} onValueChange={(value) => handleInputChange("areaUnit", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sqft">Square Feet</SelectItem>
                  <SelectItem value="sqm">Square Meters</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="siteType">Site Type</Label>
              <Select value={formData.siteType} onValueChange={(value) => handleInputChange("siteType", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="residential">Residential</SelectItem>
                  <SelectItem value="commercial">Commercial</SelectItem>
                  <SelectItem value="industrial">Industrial</SelectItem>
                  <SelectItem value="infrastructure">Infrastructure</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="estimatedCost">Estimated Cost (₹)</Label>
            <Input
              id="estimatedCost"
              type="number"
              value={formData.estimatedCost}
              onChange={(e) => handleInputChange("estimatedCost", e.target.value)}
              placeholder="e.g., 5000000"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Brief description of the project..."
              rows={3}
              className="resize-none"
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" className="flex-1 bg-black hover:bg-gray-800 text-white">
              Update Site
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SiteEditDialog;
