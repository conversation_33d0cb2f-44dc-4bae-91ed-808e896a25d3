
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Building2, DollarSign } from "lucide-react";

interface SiteOverviewCardsProps {
  site: {
    area: number;
    areaUnit: string;
    siteType: string;
    estimatedCost?: number;
  };
  totalMaterialCost: number;
  estimatedVsActual: number;
  formatCurrency: (amount: number) => string;
}

const SiteOverviewCards = ({ site, totalMaterialCost, estimatedVsActual, formatCurrency }: SiteOverviewCardsProps) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Site Area</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-xl sm:text-2xl font-bold">{site.area}</div>
          <p className="text-xs text-muted-foreground">{site.areaUnit}</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Site Type</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-xl sm:text-2xl font-bold capitalize">{site.siteType}</div>
          <p className="text-xs text-muted-foreground">Project category</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Estimated Cost</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-lg sm:text-2xl font-bold break-words">
            {site.estimatedCost ? formatCurrency(site.estimatedCost) : 'N/A'}
          </div>
          <p className="text-xs text-muted-foreground">Project budget</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Actual Spent</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-lg sm:text-2xl font-bold break-words">{formatCurrency(totalMaterialCost)}</div>
          <p className={`text-xs ${estimatedVsActual >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {estimatedVsActual >= 0 ? 'Under' : 'Over'} budget
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default SiteOverviewCards;
