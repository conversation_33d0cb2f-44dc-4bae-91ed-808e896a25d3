
import { useState } from "react";
import { useSite } from "@/contexts/SiteContext";
import { usePersonalExpense } from "@/contexts/PersonalExpenseContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Download, FileText, Calendar as CalendarIcon, TrendingUp } from "lucide-react";
import { format, subDays, subWeeks, subMonths, subYears, isAfter, isBefore } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

const ReportsSection = () => {
  const { sites, materialEntries } = useSite();
  const { expenses } = usePersonalExpense();
  const { toast } = useToast();
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [reportConfig, setReportConfig] = useState({
    type: "overall", // overall, individual, personal
    period: "monthly", // weekly, monthly, yearly, custom
    siteId: "",
    customStartDate: undefined as Date | undefined,
    customEndDate: undefined as Date | undefined,
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getDateRange = () => {
    const now = new Date();
    switch (reportConfig.period) {
      case "weekly":
        return { start: subWeeks(now, 1), end: now };
      case "monthly":
        return { start: subMonths(now, 1), end: now };
      case "yearly":
        return { start: subYears(now, 1), end: now };
      case "custom":
        return { 
          start: reportConfig.customStartDate || subMonths(now, 1), 
          end: reportConfig.customEndDate || now 
        };
      default:
        return { start: subMonths(now, 1), end: now };
    }
  };

  const generateReportData = () => {
    const { start, end } = getDateRange();
    
    if (reportConfig.type === "individual" && !reportConfig.siteId) {
      toast({
        title: "Site Required",
        description: "Please select a site for individual reports.",
        variant: "destructive",
      });
      return null;
    }

    if (reportConfig.type === "personal") {
      const filteredExpenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        return isAfter(expenseDate, start) && isBefore(expenseDate, end);
      });

      const totalExpenditure = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);
      
      const categoryBreakdown = filteredExpenses.reduce((acc, expense) => {
        acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
        return acc;
      }, {} as Record<string, number>);

      return {
        period: reportConfig.period,
        dateRange: { start, end },
        type: reportConfig.type,
        totalExpenditure,
        entriesCount: filteredExpenses.length,
        personalExpenses: filteredExpenses,
        categoryBreakdown,
      };
    }

    const filteredSites = reportConfig.type === "individual" 
      ? sites.filter(site => site.id === reportConfig.siteId)
      : sites;

    const filteredEntries = materialEntries.filter(entry => {
      const entryDate = new Date(entry.purchaseDate);
      const inDateRange = isAfter(entryDate, start) && isBefore(entryDate, end);
      const matchesSite = reportConfig.type === "individual" 
        ? entry.siteId === reportConfig.siteId 
        : true;
      return inDateRange && matchesSite;
    });

    const totalExpenditure = filteredEntries.reduce((sum, entry) => 
      sum + (entry.price * entry.quantity), 0
    );

    const siteBreakdown = filteredSites.map(site => {
      const siteEntries = filteredEntries.filter(entry => entry.siteId === site.id);
      const siteTotal = siteEntries.reduce((sum, entry) => 
        sum + (entry.price * entry.quantity), 0
      );
      
      return {
        siteName: site.siteName,
        location: site.location,
        estimatedCost: site.estimatedCost || 0,
        actualSpent: siteTotal,
        progress: site.progress || 0,
        entriesCount: siteEntries.length,
        materials: siteEntries.map(entry => ({
          material: entry.materialName,
          quantity: entry.quantity,
          price: entry.price,
          total: entry.price * entry.quantity,
          date: entry.purchaseDate,
        }))
      };
    });

    return {
      period: reportConfig.period,
      dateRange: { start, end },
      type: reportConfig.type,
      totalExpenditure,
      sitesCount: filteredSites.length,
      entriesCount: filteredEntries.length,
      siteBreakdown,
    };
  };

  const downloadReport = () => {
    const reportData = generateReportData();
    if (!reportData) return;

    let reportContent = "";

    if (reportConfig.type === "personal") {
      reportContent = `
PERSONAL EXPENSES REPORT
========================

Report Type: Personal Expenses
Period: ${reportData.period.charAt(0).toUpperCase() + reportData.period.slice(1)}
Date Range: ${format(reportData.dateRange.start, "dd/MM/yyyy")} - ${format(reportData.dateRange.end, "dd/MM/yyyy")}
Generated: ${format(new Date(), "dd/MM/yyyy HH:mm")}

SUMMARY
-------
Total Personal Expenditure: ${formatCurrency(reportData.totalExpenditure)}
Total Expense Entries: ${reportData.entriesCount}

CATEGORY BREAKDOWN
------------------
${Object.entries(reportData.categoryBreakdown || {}).map(([category, amount]) => 
  `${category}: ${formatCurrency(amount as number)}`
).join('\n')}

DETAILED EXPENSES
-----------------
${(reportData.personalExpenses || []).map((expense: any) => 
  `${format(new Date(expense.date), "dd/MM/yyyy")} - ${expense.description} (${expense.category}): ${formatCurrency(expense.amount)}`
).join('\n')}

END OF REPORT
`.trim();
    } else {
      reportContent = `
CONSTRUCTION EXPENDITURE REPORT
================================

Report Type: ${reportData.type === "overall" ? "Overall Firm" : "Individual Site"}
Period: ${reportData.period.charAt(0).toUpperCase() + reportData.period.slice(1)}
Date Range: ${format(reportData.dateRange.start, "dd/MM/yyyy")} - ${format(reportData.dateRange.end, "dd/MM/yyyy")}
Generated: ${format(new Date(), "dd/MM/yyyy HH:mm")}

SUMMARY
-------
Total Expenditure: ${formatCurrency(reportData.totalExpenditure)}
Sites Covered: ${reportData.sitesCount}
Material Entries: ${reportData.entriesCount}

SITE BREAKDOWN
--------------
${reportData.siteBreakdown?.map(site => `
Site: ${site.siteName}
Location: ${site.location}
Estimated Cost: ${formatCurrency(site.estimatedCost)}
Actual Spent: ${formatCurrency(site.actualSpent)}
Progress: ${site.progress}%
Budget Status: ${site.actualSpent > site.estimatedCost ? "Over Budget" : "Within Budget"}
Material Entries: ${site.entriesCount}

Materials:
${site.materials.map(material => 
  `  - ${material.material}: ${material.quantity} units @ ${formatCurrency(material.price)} = ${formatCurrency(material.total)} (${format(new Date(material.date), "dd/MM/yyyy")})`
).join('\n')}
`).join('\n')}

END OF REPORT
`.trim();
    }

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${reportData.type === "personal" ? "Personal_Expenses" : "Construction"}_Report_${reportData.type}_${reportData.period}_${format(new Date(), "ddMMyyyy")}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: "Report Downloaded",
      description: `Your ${reportData.type === "personal" ? "personal expenses" : "construction expenditure"} report has been generated and downloaded.`,
    });

    setIsDialogOpen(false);
  };

  return (
    <Card className="hover-lift">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-black flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Reports & Analytics
            </CardTitle>
            <CardDescription>Generate detailed expenditure reports</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-black hover:bg-gray-800 text-white">
                <FileText className="h-4 w-4 mr-2" />
                Generate Report
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Generate Construction Report</DialogTitle>
                <DialogDescription>
                  Configure your report parameters and download detailed analytics
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label>Report Type</Label>
                  <Select value={reportConfig.type} onValueChange={(value) => 
                    setReportConfig(prev => ({ ...prev, type: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="overall">Overall Firm Report</SelectItem>
                      <SelectItem value="individual">Individual Site Report</SelectItem>
                      <SelectItem value="personal">Personal Expenses Report</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {reportConfig.type === "individual" && (
                  <div className="space-y-2">
                    <Label>Select Site</Label>
                    <Select value={reportConfig.siteId} onValueChange={(value) => 
                      setReportConfig(prev => ({ ...prev, siteId: value }))
                    }>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a site" />
                      </SelectTrigger>
                      <SelectContent>
                        {sites.map(site => (
                          <SelectItem key={site.id} value={site.id}>
                            {site.siteName} - {site.location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Time Period</Label>
                  <Select value={reportConfig.period} onValueChange={(value) => 
                    setReportConfig(prev => ({ ...prev, period: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weekly">Last Week</SelectItem>
                      <SelectItem value="monthly">Last Month</SelectItem>
                      <SelectItem value="yearly">Last Year</SelectItem>
                      <SelectItem value="custom">Custom Date Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {reportConfig.period === "custom" && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Start Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !reportConfig.customStartDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {reportConfig.customStartDate ? (
                              format(reportConfig.customStartDate, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={reportConfig.customStartDate}
                            onSelect={(date) => setReportConfig(prev => ({ 
                              ...prev, 
                              customStartDate: date 
                            }))}
                            initialFocus
                            className="pointer-events-auto"
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="space-y-2">
                      <Label>End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !reportConfig.customEndDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {reportConfig.customEndDate ? (
                              format(reportConfig.customEndDate, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={reportConfig.customEndDate}
                            onSelect={(date) => setReportConfig(prev => ({ 
                              ...prev, 
                              customEndDate: date 
                            }))}
                            initialFocus
                            className="pointer-events-auto"
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                )}

                <Button 
                  onClick={downloadReport} 
                  className="w-full bg-black hover:bg-gray-800 text-white"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Report
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border rounded-lg">
            <FileText className="h-8 w-8 text-gray-600 mx-auto mb-2" />
            <h3 className="font-medium mb-1">Overall Reports</h3>
            <p className="text-sm text-gray-600">Firm-wide expenditure analysis</p>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <TrendingUp className="h-8 w-8 text-gray-600 mx-auto mb-2" />
            <h3 className="font-medium mb-1">Site Reports</h3>
            <p className="text-sm text-gray-600">Individual project analytics</p>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <CalendarIcon className="h-8 w-8 text-gray-600 mx-auto mb-2" />
            <h3 className="font-medium mb-1">Personal Expenses</h3>
            <p className="text-sm text-gray-600">Personal expenditure tracking</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReportsSection;
