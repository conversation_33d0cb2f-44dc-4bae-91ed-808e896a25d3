
import { useState, useEffect } from "react";
import { useSite } from "@/contexts/SiteContext";
import { MaterialEntry } from "@/contexts/SiteContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Package, Calendar, DollarSign, Filter, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface MaterialTrackerProps {
  siteId: string;
}

const MaterialTracker = ({ siteId }: MaterialTrackerProps) => {
  const { addMaterialEntry, getMaterialEntriesBySite } = useSite();
  const { toast } = useToast();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [materialEntries, setMaterialEntries] = useState<MaterialEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [formData, setFormData] = useState({
    materialName: "",
    quantity: "",
    price: "",
    purchaseDate: new Date().toISOString().split('T')[0],
    notes: "",
  });

  // Refresh function that can be called manually
  const refreshMaterialEntries = async () => {
    if (!siteId) return;

    setIsRefreshing(true);
    try {
      const entries = await getMaterialEntriesBySite(siteId);
      setMaterialEntries(entries);
    } catch (error) {
      console.error('Failed to refresh material entries:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh material entries.';
      toast({
        title: "Error Refreshing Materials",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Load material entries when component mounts or siteId changes
  useEffect(() => {
    const loadMaterialEntries = async () => {
      if (!siteId) return;

      setIsLoading(true);
      try {
        const entries = await getMaterialEntriesBySite(siteId);
        setMaterialEntries(entries);
      } catch (error) {
        console.error('Failed to load material entries:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to load material entries.';
        toast({
          title: "Error Loading Materials",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadMaterialEntries();
  }, [siteId, getMaterialEntriesBySite, toast]);

  const totalCost = materialEntries.reduce((sum, entry) => sum + (entry.price * entry.quantity), 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.materialName || !formData.quantity || !formData.price) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      await addMaterialEntry({
        siteId,
        materialName: formData.materialName,
        quantity: parseFloat(formData.quantity),
        price: parseFloat(formData.price),
        purchaseDate: formData.purchaseDate,
        notes: formData.notes,
      });

      toast({
        title: "Material Entry Added",
        description: `${formData.materialName} has been logged successfully.`,
      });

      setFormData({
        materialName: "",
        quantity: "",
        price: "",
        purchaseDate: new Date().toISOString().split('T')[0],
        notes: "",
      });

      setIsDialogOpen(false);

      // Refresh material entries to show the new entry
      await refreshMaterialEntries();
    } catch (error) {
      console.error('Failed to add material entry:', error);
      toast({
        title: "Error",
        description: "Failed to add material entry. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-red-50 to-purple-50 border-red-200 hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-700">Total Materials</CardTitle>
            <Package className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{materialEntries.length}</div>
            <p className="text-xs text-red-600">Material entries</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-red-50 border-purple-200 hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700">Total Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{formatCurrency(totalCost)}</div>
            <p className="text-xs text-purple-600">Material expenses</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-100 to-purple-100 border-red-300 hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">Last Purchase</CardTitle>
            <Calendar className="h-4 w-4 text-red-700" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-700">
              {materialEntries.length > 0 
                ? new Date(materialEntries[materialEntries.length - 1].purchaseDate).toLocaleDateString()
                : 'N/A'
              }
            </div>
            <p className="text-xs text-red-700">Most recent entry</p>
          </CardContent>
        </Card>
      </div>

      {/* Add Material Entry */}
      <Card className="hover-lift">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="bg-gradient-to-r from-red-600 to-purple-600 bg-clip-text text-transparent">Material Entries</CardTitle>
              <CardDescription>Track material purchases and costs</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={refreshMaterialEntries}
                disabled={isRefreshing}
                variant="outline"
                size="sm"
                className="hover:bg-gradient-to-r hover:from-red-50 hover:to-purple-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-700 hover:to-purple-700 text-white transition-all duration-300 hover-lift">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Material
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle className="bg-gradient-to-r from-red-600 to-purple-600 bg-clip-text text-transparent">Add Material Entry</DialogTitle>
                  <DialogDescription>
                    Record a new material purchase for this site
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="materialName">Material Name *</Label>
                    <Input
                      id="materialName"
                      placeholder="e.g., Cement, Steel, Bricks"
                      value={formData.materialName}
                      onChange={(e) => handleInputChange("materialName", e.target.value)}
                      className="focus:ring-red-500 focus:border-red-500"
                      required
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="quantity">Quantity *</Label>
                      <Input
                        id="quantity"
                        type="number"
                        step="0.01"
                        placeholder="e.g., 50"
                        value={formData.quantity}
                        onChange={(e) => handleInputChange("quantity", e.target.value)}
                        className="focus:ring-red-500 focus:border-red-500"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="price">Unit Price (₹) *</Label>
                      <Input
                        id="price"
                        type="number"
                        step="0.01"
                        placeholder="e.g., 450.00"
                        value={formData.price}
                        onChange={(e) => handleInputChange("price", e.target.value)}
                        className="focus:ring-red-500 focus:border-red-500"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="purchaseDate">Purchase Date *</Label>
                    <Input
                      id="purchaseDate"
                      type="date"
                      value={formData.purchaseDate}
                      onChange={(e) => handleInputChange("purchaseDate", e.target.value)}
                      className="focus:ring-red-500 focus:border-red-500"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      placeholder="Additional notes about this purchase..."
                      value={formData.notes}
                      onChange={(e) => handleInputChange("notes", e.target.value)}
                      className="focus:ring-red-500 focus:border-red-500"
                      rows={3}
                    />
                  </div>

                  <Button type="submit" className="w-full bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-700 hover:to-purple-700 text-white">
                    Add Material Entry
                  </Button>
                </form>
              </DialogContent>
            </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-pulse" />
              <p className="text-gray-500 mb-4">Loading material entries...</p>
            </div>
          ) : materialEntries.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No material entries yet</p>
              <p className="text-sm text-gray-400">Add your first material purchase to start tracking</p>
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Material</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Total Cost</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {materialEntries.map((entry) => (
                    <TableRow key={entry.id} className="hover:bg-gradient-to-r hover:from-red-50 hover:to-purple-50 transition-all duration-300">
                      <TableCell className="font-medium">{entry.materialName}</TableCell>
                      <TableCell>{entry.quantity}</TableCell>
                      <TableCell>{formatCurrency(entry.price)}</TableCell>
                      <TableCell className="font-semibold text-purple-700">{formatCurrency(entry.quantity * entry.price)}</TableCell>
                      <TableCell>
                        {new Date(entry.purchaseDate).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MaterialTracker;
