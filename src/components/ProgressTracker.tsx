
import { useState, useEffect } from "react";
import { useSite, ProgressEntry } from "@/contexts/SiteContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Camera, Calendar, Image as ImageIcon, Upload, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ProgressTrackerProps {
  siteId: string;
}

const ProgressTracker = ({ siteId }: ProgressTrackerProps) => {
  const { addProgressEntry, getProgressEntriesBySite } = useSite();
  const { toast } = useToast();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [progressEntries, setProgressEntries] = useState<ProgressEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    notes: "",
  });
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  // Refresh function that can be called manually
  const refreshProgressEntries = async () => {
    if (!siteId) return;

    setIsRefreshing(true);
    try {
      const entries = await getProgressEntriesBySite(siteId);
      // Ensure entries is always an array
      setProgressEntries(Array.isArray(entries) ? entries : []);
    } catch (error) {
      console.error('Failed to refresh progress entries:', error);
      setProgressEntries([]);
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh progress entries.';
      toast({
        title: "Error Refreshing Progress",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Load progress entries when component mounts or siteId changes
  useEffect(() => {
    const loadProgressEntries = async () => {
      if (!siteId) return;

      setIsLoading(true);
      try {
        const entries = await getProgressEntriesBySite(siteId);
        // Ensure entries is always an array
        setProgressEntries(Array.isArray(entries) ? entries : []);
      } catch (error) {
        console.error('Failed to load progress entries:', error);
        setProgressEntries([]);
        const errorMessage = error instanceof Error ? error.message : 'Failed to load progress entries.';
        toast({
          title: "Error Loading Progress",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadProgressEntries();
  }, [siteId, getProgressEntriesBySite, toast]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB limit
      
      if (!isValidType) {
        toast({
          title: "Invalid File Type",
          description: "Please upload only image files.",
          variant: "destructive",
        });
        return false;
      }
      
      if (!isValidSize) {
        toast({
          title: "File Too Large",
          description: "Please upload images smaller than 5MB.",
          variant: "destructive",
        });
        return false;
      }
      
      return true;
    });
    
    setSelectedFiles(prev => [...prev, ...validFiles]);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.notes && selectedFiles.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please add notes or upload photos for this progress entry.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Convert files to URLs (in a real app, you'd upload to a server)
      const photoUrls = selectedFiles.map(file => URL.createObjectURL(file));

      await addProgressEntry({
        siteId,
        date: formData.date,
        notes: formData.notes,
        photos: photoUrls,
      });

      toast({
        title: "Progress Entry Added",
        description: "Site progress has been recorded successfully.",
      });

      setFormData({
        date: new Date().toISOString().split('T')[0],
        notes: "",
      });
      setSelectedFiles([]);
      setIsDialogOpen(false);

      // Refresh progress entries to show the new entry
      await refreshProgressEntries();
    } catch (error) {
      console.error('Failed to add progress entry:', error);
      toast({
        title: "Error",
        description: "Failed to add progress entry. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Progress Timeline</CardTitle>
              <CardDescription>Track construction progress with photos and notes</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={refreshProgressEntries}
                disabled={isRefreshing}
                variant="outline"
                size="sm"
                className="hover:bg-gradient-to-r hover:from-orange-50 hover:to-orange-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-orange-600 hover:bg-orange-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Progress
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Add Progress Entry</DialogTitle>
                  <DialogDescription>
                    Record site progress with photos and notes
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Date *</Label>
                    <Input
                      id="date"
                      type="date"
                      value={formData.date}
                      onChange={(e) => handleInputChange("date", e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="photos">Upload Photos</Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                      <input
                        id="photos"
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={handleFileChange}
                        className="hidden"
                      />
                      <label htmlFor="photos" className="cursor-pointer">
                        <div className="space-y-2">
                          <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                          <p className="text-sm text-gray-600">Click to upload progress photos</p>
                          <p className="text-xs text-gray-500">Multiple images, max 5MB each</p>
                        </div>
                      </label>
                    </div>
                    
                    {selectedFiles.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <Label>Selected Photos ({selectedFiles.length})</Label>
                        <div className="grid grid-cols-2 gap-2">
                          {selectedFiles.map((file, index) => (
                            <div key={index} className="relative">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Preview ${index + 1}`}
                                className="w-full h-20 object-cover rounded border"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                onClick={() => removeFile(index)}
                              >
                                ×
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Progress Notes</Label>
                    <Textarea
                      id="notes"
                      placeholder="Describe the progress made, challenges faced, or next steps..."
                      value={formData.notes}
                      onChange={(e) => handleInputChange("notes", e.target.value)}
                      rows={4}
                    />
                  </div>

                  <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700">
                    Add Progress Entry
                  </Button>
                </form>
              </DialogContent>
            </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-12">
              <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-pulse" />
              <p className="text-gray-500 mb-4">Loading progress entries...</p>
            </div>
          ) : progressEntries.length === 0 ? (
            <div className="text-center py-12">
              <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No progress entries yet</p>
              <p className="text-sm text-gray-400">Add your first progress update to start tracking</p>
            </div>
          ) : (
            <div className="space-y-6">
              {progressEntries.map((entry, index) => (
                <div key={entry.id} className="border-l-4 border-orange-500 pl-6 pb-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-orange-500" />
                    <span className="font-medium">{new Date(entry.date).toLocaleDateString()}</span>
                    <span className="text-sm text-gray-500">
                      Entry #{progressEntries.length - index}
                    </span>
                  </div>
                  
                  {entry.notes && (
                    <div className="mb-4">
                      <p className="text-gray-700 whitespace-pre-wrap">{entry.notes}</p>
                    </div>
                  )}
                  
                  {entry.photos.length > 0 && (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <ImageIcon className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600">
                          Progress Photos ({entry.photos.length})
                        </span>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                        {entry.photos.map((photo, photoIndex) => (
                          <div key={photoIndex} className="relative group">
                            <img
                              src={photo}
                              alt={`Progress photo ${photoIndex + 1}`}
                              className="w-full h-24 object-cover rounded border hover:opacity-75 transition-opacity cursor-pointer"
                              onClick={() => window.open(photo, '_blank')}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ProgressTracker;
