
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Link, useLocation } from "react-router-dom";
import { LogOut, Home, Plus, FileText, Wallet } from "lucide-react";

const Navigation = () => {
  const { user, logout } = useAuth();
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="bg-black text-white shadow-lg border-b border-gray-800">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-8">
            <Link to="/" className="flex items-center space-x-2 hover-lift">
              <div className="bg-white text-black p-2 rounded-lg animate-pulse-glow">
                <FileText className="h-6 w-6" />
              </div>
              <span className="text-xl font-bold">ConstructPro</span>
            </Link>
            
            <div className="flex space-x-4">
              <Link
                to="/dashboard"
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-all duration-300 hover-lift ${
                  isActive('/dashboard') || isActive('/') 
                    ? 'bg-white/20 backdrop-blur-sm' 
                    : 'hover:bg-white/10'
                }`}
              >
                <Home className="h-4 w-4" />
                <span>Dashboard</span>
              </Link>
              
              <Link
                to="/create-site"
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-all duration-300 hover-lift ${
                  isActive('/create-site') 
                    ? 'bg-white/20 backdrop-blur-sm' 
                    : 'hover:bg-white/10'
                }`}
              >
                <Plus className="h-4 w-4" />
                <span>New Site</span>
              </Link>

              <Link
                to="/personal-expenses"
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-all duration-300 hover-lift ${
                  isActive('/personal-expenses') 
                    ? 'bg-white/20 backdrop-blur-sm' 
                    : 'hover:bg-white/10'
                }`}
              >
                <Wallet className="h-4 w-4" />
                <span>Personal Expenses</span>
              </Link>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-sm animate-slide-in-right">
              <div className="font-medium">{user?.firmName}</div>
              <div className="text-gray-400">{user?.email}</div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={logout}
              className="border-2 border-white bg-white text-black hover:bg-gray-100 hover:border-gray-200 transition-all duration-300 hover-lift font-semibold"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
