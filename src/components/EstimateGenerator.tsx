
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Calculator, Download, FileText, TrendingUp } from "lucide-react";
import { Material } from "@/contexts/SiteContext";

interface EstimateGeneratorProps {
  area: number;
  areaUnit: "sqft" | "sqm";
  siteType: "residential" | "commercial" | "industrial" | "infrastructure";
  siteName: string;
}

const EstimateGenerator = ({ area, areaUnit, siteType, siteName }: EstimateGeneratorProps) => {
  const [estimate, setEstimate] = useState<{
    materials: Material[];
    totalCost: number;
    laborCost: number;
    overheadCost: number;
  } | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Base material requirements per sq ft with INR pricing
  const baseMaterials = {
    residential: [
      { name: "Cement", ratePerSqft: 0.8, unit: "bags", pricePerUnit: 450 },
      { name: "Sand", ratePerSqft: 1.2, unit: "cubic ft", pricePerUnit: 75 },
      { name: "Gravel", ratePerSqft: 1.5, unit: "cubic ft", pricePerUnit: 90 },
      { name: "Steel Rebar", ratePerSqft: 8, unit: "kg", pricePerUnit: 65 },
      { name: "Bricks", ratePerSqft: 12, unit: "pieces", pricePerUnit: 12 },
      { name: "Lumber", ratePerSqft: 2.5, unit: "board ft", pricePerUnit: 120 },
      { name: "Roofing Material", ratePerSqft: 1.1, unit: "sq ft", pricePerUnit: 240 },
    ],
    commercial: [
      { name: "Cement", ratePerSqft: 1.2, unit: "bags", pricePerUnit: 450 },
      { name: "Sand", ratePerSqft: 1.8, unit: "cubic ft", pricePerUnit: 75 },
      { name: "Gravel", ratePerSqft: 2.2, unit: "cubic ft", pricePerUnit: 90 },
      { name: "Steel Rebar", ratePerSqft: 12, unit: "kg", pricePerUnit: 65 },
      { name: "Concrete Blocks", ratePerSqft: 15, unit: "pieces", pricePerUnit: 75 },
      { name: "Steel Beams", ratePerSqft: 0.8, unit: "linear ft", pricePerUnit: 750 },
      { name: "Glass Panels", ratePerSqft: 0.3, unit: "sq ft", pricePerUnit: 1350 },
    ],
    industrial: [
      { name: "Cement", ratePerSqft: 1.5, unit: "bags", pricePerUnit: 450 },
      { name: "Sand", ratePerSqft: 2.2, unit: "cubic ft", pricePerUnit: 75 },
      { name: "Gravel", ratePerSqft: 2.8, unit: "cubic ft", pricePerUnit: 90 },
      { name: "Steel Rebar", ratePerSqft: 18, unit: "kg", pricePerUnit: 65 },
      { name: "Steel Beams", ratePerSqft: 1.5, unit: "linear ft", pricePerUnit: 750 },
      { name: "Industrial Flooring", ratePerSqft: 1, unit: "sq ft", pricePerUnit: 450 },
      { name: "Insulation", ratePerSqft: 1, unit: "sq ft", pricePerUnit: 105 },
    ],
    infrastructure: [
      { name: "Cement", ratePerSqft: 2, unit: "bags", pricePerUnit: 450 },
      { name: "Sand", ratePerSqft: 3, unit: "cubic ft", pricePerUnit: 75 },
      { name: "Gravel", ratePerSqft: 4, unit: "cubic ft", pricePerUnit: 90 },
      { name: "Steel Rebar", ratePerSqft: 25, unit: "kg", pricePerUnit: 65 },
      { name: "Concrete", ratePerSqft: 0.33, unit: "cubic meters", pricePerUnit: 3600 },
      { name: "Asphalt", ratePerSqft: 0.08, unit: "tons", pricePerUnit: 4500 },
    ],
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const generateEstimate = () => {
    setIsGenerating(true);
    
    setTimeout(() => {
      const areaInSqft = areaUnit === "sqm" ? area * 10.764 : area;
      const materialList = baseMaterials[siteType] || baseMaterials.residential;
      
      const materials: Material[] = materialList.map((material, index) => ({
        id: (index + 1).toString(),
        name: material.name,
        estimatedQuantity: Math.ceil(material.ratePerSqft * areaInSqft),
        unit: material.unit,
        estimatedPrice: material.pricePerUnit,
      }));

      const materialsCost = materials.reduce((sum, material) => 
        sum + (material.estimatedQuantity * material.estimatedPrice), 0
      );

      const laborMultiplier = {
        residential: 0.6,
        commercial: 0.8,
        industrial: 1.0,
        infrastructure: 1.2,
      };

      const laborCost = materialsCost * laborMultiplier[siteType];
      const overheadCost = (materialsCost + laborCost) * 0.15;
      const totalCost = materialsCost + laborCost + overheadCost;

      setEstimate({
        materials,
        totalCost,
        laborCost,
        overheadCost,
      });
      
      setIsGenerating(false);
    }, 1500);
  };

  const exportEstimate = () => {
    if (!estimate) return;

    const content = `
CONSTRUCTION COST ESTIMATE
Site: ${siteName}
Area: ${area} ${areaUnit}
Type: ${siteType.charAt(0).toUpperCase() + siteType.slice(1)}
Generated: ${new Date().toLocaleDateString('en-IN')}

MATERIALS:
${estimate.materials.map(m => 
  `${m.name}: ${m.estimatedQuantity} ${m.unit} @ ${formatCurrency(m.estimatedPrice)} = ${formatCurrency(m.estimatedQuantity * m.estimatedPrice)}`
).join('\n')}

COST BREAKDOWN:
Materials Cost: ${formatCurrency(estimate.materials.reduce((sum, m) => sum + (m.estimatedQuantity * m.estimatedPrice), 0))}
Labor Cost: ${formatCurrency(estimate.laborCost)}
Overhead (15%): ${formatCurrency(estimate.overheadCost)}

TOTAL ESTIMATED COST: ${formatCurrency(estimate.totalCost)}

Note: This is a preliminary estimate based on standard rates for Indian market. Actual costs may vary based on local market conditions, specific requirements, and material quality.
    `;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${siteName.replace(/\s+/g, '_')}_estimate.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {!estimate ? (
        <div className="text-center py-8 animate-fade-in-scale">
          <Button 
            onClick={generateEstimate} 
            disabled={isGenerating}
            className="bg-black hover:bg-gray-800 text-white transition-all duration-300 hover-lift"
          >
            {isGenerating ? (
              <>
                <Calculator className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Calculator className="h-4 w-4 mr-2" />
                Generate Estimate
              </>
            )}
          </Button>
          <p className="text-sm text-gray-500 mt-2">
            Click to generate cost estimates based on Indian market rates
          </p>
        </div>
      ) : (
        <div className="space-y-6 animate-fade-in-scale">
          {/* Cost Summary */}
          <div className="grid grid-cols-2 gap-4">
            <Card className="bg-gray-50 border-gray-200 hover-lift">
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-black">
                    {formatCurrency(estimate.totalCost)}
                  </div>
                  <div className="text-sm text-gray-800">Total Estimated Cost</div>
                  <TrendingUp className="h-5 w-5 text-gray-500 mx-auto mt-2" />
                </div>
              </CardContent>
            </Card>
            <Card className="bg-gray-50 border-gray-200 hover-lift">
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-xl font-bold text-black">
                    {formatCurrency(estimate.totalCost / area)}
                  </div>
                  <div className="text-sm text-gray-800">Cost per {areaUnit}</div>
                  <div className="text-xs text-gray-600 mt-1">Indian Market Rate</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Cost Breakdown */}
          <Card className="hover-lift">
            <CardHeader>
              <CardTitle className="text-lg flex items-center text-black">
                <TrendingUp className="h-5 w-5 mr-2 text-black" />
                Cost Breakdown
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Materials</span>
                  <span className="font-medium">
                    {formatCurrency(estimate.materials.reduce((sum, m) => sum + (m.estimatedQuantity * m.estimatedPrice), 0))}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Labor</span>
                  <span className="font-medium">{formatCurrency(estimate.laborCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Overhead (15%)</span>
                  <span className="font-medium">{formatCurrency(estimate.overheadCost)}</span>
                </div>
                <hr />
                <div className="flex justify-between font-bold text-lg text-black">
                  <span>Total</span>
                  <span>{formatCurrency(estimate.totalCost)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Materials List */}
          <Card className="hover-lift">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-black">Materials Required</CardTitle>
                <Button onClick={exportEstimate} variant="outline" size="sm" className="hover-lift">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Material</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {estimate.materials.map((material) => (
                    <TableRow key={material.id} className="hover:bg-gray-50 transition-colors">
                      <TableCell className="font-medium">{material.name}</TableCell>
                      <TableCell>{material.estimatedQuantity} {material.unit}</TableCell>
                      <TableCell>{formatCurrency(material.estimatedPrice)}</TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(material.estimatedQuantity * material.estimatedPrice)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 animate-slide-in-right">
            <div className="flex items-start space-x-2">
              <FileText className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Disclaimer</p>
                <p>This is a preliminary estimate based on Indian market rates. Actual costs may vary based on local market conditions, specific requirements, and material quality. GST and other taxes are not included.</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EstimateGenerator;
