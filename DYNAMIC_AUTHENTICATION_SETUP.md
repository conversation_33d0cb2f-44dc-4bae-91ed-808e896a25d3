# Dynamic Authentication System Setup

This document describes the new dynamic authentication system that has replaced all hardcoded credentials with environment-based configuration.

## Overview

The authentication system now supports:
- **Environment-based configuration** for all sensitive data
- **Dynamic SuperAdmin creation** from environment variables
- **Secure organization admin credential generation**
- **Database-driven authentication** for both user types
- **No hardcoded credentials** anywhere in the codebase

## Required Environment Variables

### Database Configuration
```bash
DB_DEFAULT_CONNECTION=Server=localhost,1433;Database=SiteStrideManager;User Id=sa;Password=YourPassword;TrustServerCertificate=true;
DB_SUPERADMIN_CONNECTION=Server=localhost,1433;Database=SiteStrideManagerSuperAdmin;User Id=sa;Password=YourPassword;TrustServerCertificate=true;
```

### JWT Configuration
```bash
JWT_SECRET_KEY=YourSuperSecretKeyThatIsAtLeast32CharactersLongAndSecure!
JWT_ISSUER=SiteStrideManager
JWT_AUDIENCE=SiteStrideManager
JWT_EXPIRY_MINUTES=60
```

### SuperAdmin Initial Setup
```bash
SUPERADMIN_EMAIL=<EMAIL>
SUPERADMIN_PASSWORD=YourSecurePassword123!
SUPERADMIN_FIRST_NAME=Super
SUPERADMIN_LAST_NAME=Admin
SUPERADMIN_PHONE=******-0123
```

### Application Configuration
```bash
APPLICATION_BASE_URL=http://localhost:8080
CORS_ORIGINS=http://localhost:8080,http://localhost:8081,http://localhost:3000,http://localhost:5173
```

## Setup Instructions

### 1. Environment Configuration

**Option A: Using .env file (Development)**
1. Copy the example file: `cp backend/SiteStrideManager.API/.env.example backend/SiteStrideManager.API/.env`
2. Edit the `.env` file with your actual values
3. The application will automatically load these on startup

**Option B: System Environment Variables (Production)**
Set the environment variables in your deployment environment:
```bash
export DB_DEFAULT_CONNECTION="Server=your-server;Database=SiteStrideManager;..."
export JWT_SECRET_KEY="your-secure-secret-key"
# ... set all other required variables
```

### 2. Database Setup

1. Ensure your SQL Server is running
2. The application will automatically create databases on first startup
3. The initial SuperAdmin user will be created from environment variables

### 3. First Run

1. Set all required environment variables
2. Start the application: `dotnet run` (from backend/SiteStrideManager.API/)
3. The system will:
   - Create databases if they don't exist
   - Create the initial SuperAdmin user
   - Be ready for authentication

## Authentication Flow

### SuperAdmin Authentication
1. SuperAdmin logs in with credentials set in environment variables
2. Credentials are validated against the SuperAdmin database
3. JWT token is generated and returned

### Organization Admin Authentication
1. SuperAdmin creates a new organization through the admin panel
2. SuperAdmin creates an organization admin user
3. System generates secure temporary credentials
4. Organization admin logs in and must change password on first login
5. Subsequent logins use the updated credentials from the database

## Security Features

### Password Security
- All passwords are hashed using BCrypt with salt
- Temporary passwords are generated using cryptographically secure random number generator
- 16-character passwords with mixed case, numbers, and special characters

### Configuration Security
- No hardcoded credentials in source code
- All sensitive data loaded from environment variables
- Fallback to configuration files only for non-sensitive defaults

### Database Security
- Separate databases for SuperAdmin and organization data
- Proper data isolation between organizations
- Audit logging for SuperAdmin actions

## Migration from Previous System

The old hardcoded credentials have been:
- ✅ Removed from all source files
- ✅ Commented out in SQL scripts with migration instructions
- ✅ Replaced with environment-based configuration
- ✅ Enhanced with secure password generation

## Troubleshooting

### Common Issues

**"JWT setting 'SecretKey' is required but not configured"**
- Ensure `JWT_SECRET_KEY` environment variable is set
- Key must be at least 32 characters long

**"Database connection string is required but not configured"**
- Set `DB_DEFAULT_CONNECTION` and `DB_SUPERADMIN_CONNECTION`
- Verify SQL Server is accessible with provided credentials

**"SuperAdmin environment variables not set"**
- Set all required SUPERADMIN_* environment variables
- These are only needed for initial setup

### Verification Steps

1. Check environment variables are loaded:
   ```bash
   echo $JWT_SECRET_KEY  # Should show your secret key
   ```

2. Verify database connectivity:
   - Check SQL Server is running
   - Test connection strings manually

3. Check application logs for detailed error messages

## Production Deployment

1. Set all environment variables in your deployment environment
2. Ensure secure secret keys (use key generators)
3. Use strong database passwords
4. Configure CORS origins for your production domains
5. Consider using Azure Key Vault or similar for secret management

## Support

If you encounter issues:
1. Check the application logs in `logs/sitestride-*.txt`
2. Verify all required environment variables are set
3. Ensure database connectivity
4. Review this documentation for proper configuration
