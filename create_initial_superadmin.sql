-- Create initial SuperAdmin user for local development
-- This script creates a SuperAdmin user with email: <EMAIL> and password: admin123

USE SiteStrideManager;

-- Create SuperAdmins table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SuperAdmins' AND xtype='U')
BEGIN
    CREATE TABLE SuperAdmins (
        Id NVARCHAR(450) NOT NULL PRIMARY KEY,
        Email NVARCHAR(255) NOT NULL,
        PasswordHash NVARCHAR(MAX) NOT NULL,
        FirstName NVARCHAR(100) NOT NULL,
        LastName NVARCHAR(100) NOT NULL,
        Phone NVARCHAR(50) NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        IsFirstLogin BIT NOT NULL DEFAULT 1,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        LastLoginAt DATETIME2 NULL
    );
    
    CREATE UNIQUE INDEX IX_SuperAdmins_Email ON SuperAdmins (Email);
END

-- Create Firms table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Firms' AND xtype='U')
BEGIN
    CREATE TABLE Firms (
        Id NVARCHAR(450) NOT NULL PRIMARY KEY,
        Name NVARCHAR(200) NOT NULL,
        Description NVARCHAR(500) NULL,
        Address NVARCHAR(500) NOT NULL,
        Contact NVARCHAR(50) NOT NULL,
        Email NVARCHAR(255) NULL,
        LogoUrl NVARCHAR(500) NULL,
        Website NVARCHAR(100) NULL,
        TaxId NVARCHAR(50) NULL,
        RegistrationNumber NVARCHAR(50) NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        SubscriptionStatus NVARCHAR(50) NOT NULL DEFAULT 'Active',
        SubscriptionExpiryDate DATETIME2 NULL,
        MaxUsers INT NOT NULL DEFAULT 10,
        MaxSites INT NOT NULL DEFAULT 50
    );
    
    CREATE INDEX IX_Firms_Name ON Firms (Name);
    CREATE INDEX IX_Firms_IsActive ON Firms (IsActive);
END

-- Create FirmSubscriptions table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FirmSubscriptions' AND xtype='U')
BEGIN
    CREATE TABLE FirmSubscriptions (
        Id NVARCHAR(450) NOT NULL PRIMARY KEY,
        FirmId NVARCHAR(450) NOT NULL,
        PlanName NVARCHAR(50) NOT NULL,
        MonthlyPrice DECIMAL(10,2) NOT NULL,
        StartDate DATETIME2 NOT NULL,
        EndDate DATETIME2 NOT NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (FirmId) REFERENCES Firms (Id) ON DELETE CASCADE
    );
    
    CREATE INDEX IX_FirmSubscriptions_FirmId ON FirmSubscriptions (FirmId);
END

-- Create SuperAdminAuditLogs table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SuperAdminAuditLogs' AND xtype='U')
BEGIN
    CREATE TABLE SuperAdminAuditLogs (
        Id NVARCHAR(450) NOT NULL PRIMARY KEY,
        SuperAdminId NVARCHAR(450) NOT NULL,
        Action NVARCHAR(100) NOT NULL,
        Description NVARCHAR(500) NULL,
        TargetType NVARCHAR(100) NULL,
        TargetId NVARCHAR(450) NULL,
        OldValues NVARCHAR(MAX) NULL,
        NewValues NVARCHAR(MAX) NULL,
        IpAddress NVARCHAR(50) NULL,
        UserAgent NVARCHAR(500) NULL,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (SuperAdminId) REFERENCES SuperAdmins (Id) ON DELETE CASCADE
    );
    
    CREATE INDEX IX_SuperAdminAuditLogs_SuperAdminId ON SuperAdminAuditLogs (SuperAdminId);
END

-- DEPRECATED: Hardcoded SuperAdmin credentials have been removed for security
-- SuperAdmin users are now created dynamically through the DatabaseSeedingService
-- Configure the following environment variables to create the initial SuperAdmin:
-- SUPERADMIN_EMAIL=<EMAIL>
-- SUPERADMIN_PASSWORD=your-secure-password
-- SUPERADMIN_FIRST_NAME=Your First Name
-- SUPERADMIN_LAST_NAME=Your Last Name
-- SUPERADMIN_PHONE=******-0123 (optional)

-- The following INSERT statement has been commented out for security:
/*
-- Insert initial SuperAdmin user
-- Email: <EMAIL>
-- Password: admin123 (BCrypt hashed)
IF NOT EXISTS (SELECT 1 FROM SuperAdmins WHERE Email = '<EMAIL>')
BEGIN
    INSERT INTO SuperAdmins (
        Id,
        Email,
        PasswordHash,
        FirstName,
        LastName,
        IsFirstLogin,
        IsActive,
        CreatedAt,
        UpdatedAt
    ) VALUES (
        'superadmin-001',
        '<EMAIL>',
        '$2a$11$rQZZZZZZZZZZZZZZZZZZZOeH8eH8eH8eH8eH8eH8eH8eH8eH8eH8eH8e', -- This is 'admin123' hashed with BCrypt
        'Super',
        'Admin',
        1,
        1,
        GETUTCDATE(),
        GETUTCDATE()
    );

    PRINT 'Initial SuperAdmin user created successfully!';
    PRINT 'Email: <EMAIL>';
    PRINT 'Password: admin123';
END
ELSE
BEGIN
    PRINT 'SuperAdmin user already exists.';
END
*/

-- Sample firm creation has been commented out - firms should be created through the SuperAdmin panel
/*
-- Create a sample firm for testing
IF NOT EXISTS (SELECT 1 FROM Firms WHERE Id = 'firm-001')
BEGIN
    INSERT INTO Firms (
        Id,
        Name,
        Address,
        Contact,
        Email,
        IsActive,
        SubscriptionStatus,
        MaxUsers,
        MaxSites,
        CreatedAt,
        UpdatedAt
    ) VALUES (
        'firm-001',
        'Sample Construction Company',
        '123 Main Street, City, State 12345',
        '******-0123',
        '<EMAIL>',
        1,
        'Active',
        10,
        50,
        GETUTCDATE(),
        GETUTCDATE()
    );

    PRINT 'Sample firm created successfully!';
END
ELSE
BEGIN
    PRINT 'Sample firm already exists.';
END
*/
