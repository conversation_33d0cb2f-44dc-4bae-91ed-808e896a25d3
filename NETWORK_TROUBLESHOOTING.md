# Network Connectivity Troubleshooting Guide

## Issue Summary
This document addresses network connectivity issues between the frontend application and backend API, specifically "net::ERR_INSUFFICIENT_RESOURCES" and "Failed to fetch" errors.

## Root Cause Analysis
The primary cause of these errors was that the backend server was not running on port 5001, causing the frontend to fail when attempting to connect to the API endpoints.

## Solution Steps

### 1. Start the Backend Server
The backend server must be running before the frontend can communicate with it.

**Quick Start:**
```bash
# From the project root directory
./start-backend.sh
```

**Manual Start:**
```bash
# Navigate to backend directory
cd backend/SiteStrideManager.API

# Start the server
dotnet run
```

### 2. Verify Backend is Running
Check that the server is listening on port 5001:

```bash
# Check if port 5001 is listening
lsof -i :5001

# Test the API endpoint
curl http://localhost:5001/swagger
```

Expected output: The server should be listening on both IPv4 and IPv6, and the swagger endpoint should return a 301 redirect.

### 3. Test API Endpoints
The failing endpoints should now work with proper authentication:

```bash
# Test progress endpoint (requires authentication)
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:5001/api/sites/SITE_ID/progress

# Test materials endpoint (requires authentication)
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:5001/api/sites/SITE_ID/materials
```

## Configuration Details

### Backend Configuration
- **Port:** 5001 (configured in `launchSettings.json`)
- **CORS:** Configured to allow frontend origins (localhost:8080, 8081, 3000, 5173)
- **Authentication:** JWT Bearer tokens required for protected endpoints
- **Database:** SQL Server on localhost:1433

### Frontend Configuration
- **API Base URL:** http://localhost:5001/api (configured in `src/lib/api.ts`)
- **Frontend Port:** 8080 (Vite development server)
- **Authentication:** JWT tokens stored in localStorage

## Common Issues and Solutions

### Issue 1: "net::ERR_INSUFFICIENT_RESOURCES"
**Cause:** Backend server not running
**Solution:** Start the backend server using the steps above

### Issue 2: "Failed to fetch"
**Cause:** Network connection refused (server not listening)
**Solution:** Ensure backend is running and listening on port 5001

### Issue 3: 401 Unauthorized
**Cause:** Missing or invalid authentication token
**Solution:** Ensure user is logged in and token is valid

### Issue 4: CORS Errors
**Cause:** Frontend origin not allowed by backend CORS policy
**Solution:** Verify CORS configuration in `Program.cs` includes your frontend URL

## Verification Checklist

- [ ] Backend server is running on port 5001
- [ ] Port 5001 is listening (check with `lsof -i :5001`)
- [ ] Swagger UI is accessible at http://localhost:5001/swagger
- [ ] Frontend can reach the backend (no CORS errors)
- [ ] Authentication is working (valid JWT tokens)
- [ ] API endpoints return expected responses

## Development Workflow

1. **Start Backend First:**
   ```bash
   ./start-backend.sh
   ```

2. **Start Frontend:**
   ```bash
   npm run dev
   ```

3. **Verify Connection:**
   - Frontend should load without network errors
   - API calls should succeed after authentication

## Monitoring and Logs

### Backend Logs
Monitor the backend console output for:
- Server startup messages
- Request/response logs
- Authentication failures
- Database connection issues

### Frontend Logs
Check browser console for:
- Network errors
- Authentication issues
- API response errors

## Additional Notes

- The backend uses Entity Framework with SQL Server
- JWT tokens expire after 60 minutes (configurable)
- CORS is configured for development origins
- The server supports both HTTP and HTTPS (development profile)
