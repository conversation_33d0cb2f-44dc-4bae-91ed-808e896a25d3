# Frontend

This directory contains the frontend application built with React, Vite, and TypeScript.

## Getting Started

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Build for production:
   ```bash
   npm run build
   ```

## Project Structure

- `src/` - Source code
  - `components/` - Reusable UI components
  - `contexts/` - React contexts
  - `hooks/` - Custom React hooks
  - `lib/` - Utility libraries
  - `pages/` - Page components
- `public/` - Static assets
- `index.html` - Main HTML template

## Technologies Used

- React 18
- TypeScript
- Vite
- Tailwind CSS
- Radix UI components
- React Router
- React Query
- Shadcn/ui components
