using System.Security.Claims;
using SiteStrideManager.API.Services;

namespace SiteStrideManager.API.Middleware;

public class TenantValidationMiddleware
{
    private readonly RequestDelegate _next;

    public TenantValidationMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context, ITenantContextService tenantContextService)
    {
        // Skip validation for SuperAdmin endpoints and auth endpoints
        var path = context.Request.Path.Value?.ToLower();
        if (path != null && (path.StartsWith("/api/superadmin") || 
                           path.StartsWith("/api/auth") || 
                           path.StartsWith("/swagger") ||
                           path.StartsWith("/api/superadmin/auth")))
        {
            await _next(context);
            return;
        }

        // Only validate for authenticated users
        if (context.User.Identity?.IsAuthenticated == true)
        {
            var userType = context.User.FindFirst("userType")?.Value;
            
            // Skip validation for SuperAdmin users
            if (userType == "SuperAdmin")
            {
                await _next(context);
                return;
            }

            var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var firmId = context.User.FindFirst("firmId")?.Value;

            if (!string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(firmId))
            {
                // Validate that the user's firm is still active
                if (!await tenantContextService.IsFirmActiveAsync(firmId))
                {
                    context.Response.StatusCode = 403;
                    await context.Response.WriteAsync("Firm access has been suspended");
                    return;
                }

                // Validate that the user still belongs to the firm
                if (!await tenantContextService.ValidateUserBelongsToFirmAsync(userId, firmId))
                {
                    context.Response.StatusCode = 403;
                    await context.Response.WriteAsync("User access has been revoked");
                    return;
                }
            }
        }

        await _next(context);
    }
}
