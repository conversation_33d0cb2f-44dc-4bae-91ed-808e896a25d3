using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.Models;

public class MaterialEntry
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string SiteId { get; set; } = string.Empty;
    
    [Required]
    public string MaterialName { get; set; } = string.Empty;
    
    public decimal Quantity { get; set; }
    
    [Required]
    public string Unit { get; set; } = string.Empty;
    
    public decimal PricePerUnit { get; set; }
    
    public string? Supplier { get; set; }
    
    public string? Notes { get; set; }
    
    public DateTime PurchaseDate { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Site Site { get; set; } = null!;
}
