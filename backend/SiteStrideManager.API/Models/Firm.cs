using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.Models;

public class Firm
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string? Description { get; set; }
    
    [Required]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;
    
    [Required]
    [StringLength(50)]
    public string Contact { get; set; } = string.Empty;
    
    [EmailAddress]
    [StringLength(255)]
    public string? Email { get; set; }
    
    [StringLength(500)]
    public string? LogoUrl { get; set; }
    
    [StringLength(100)]
    public string? Website { get; set; }
    
    [StringLength(50)]
    public string? TaxId { get; set; }
    
    [StringLength(50)]
    public string? RegistrationNumber { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    [StringLength(50)]
    public string SubscriptionStatus { get; set; } = "Active"; // Active, Suspended, Expired
    
    public DateTime? SubscriptionExpiryDate { get; set; }
    
    public int MaxUsers { get; set; } = 10; // Maximum users allowed for this firm
    
    public int MaxSites { get; set; } = 50; // Maximum sites allowed for this firm
    
    // Navigation properties
    public virtual ICollection<User> Users { get; set; } = new List<User>();
    public virtual ICollection<FirmSubscription> Subscriptions { get; set; } = new List<FirmSubscription>();
}

public class FirmSubscription
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string FirmId { get; set; } = string.Empty;
    
    [Required]
    [StringLength(50)]
    public string PlanName { get; set; } = string.Empty; // Basic, Premium, Enterprise
    
    public decimal MonthlyPrice { get; set; }
    
    public DateTime StartDate { get; set; }
    
    public DateTime EndDate { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Firm Firm { get; set; } = null!;
}
