using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.Models;

public class Site
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [Required]
    public string SiteName { get; set; } = string.Empty;
    
    [Required]
    public string Location { get; set; } = string.Empty;
    
    public decimal Area { get; set; }
    
    [Required]
    public string AreaUnit { get; set; } = string.Empty;
    
    [Required]
    public string SiteType { get; set; } = string.Empty;
    
    public decimal? EstimatedCost { get; set; }
    
    public decimal ActualCost { get; set; } = 0;
    
    public decimal Progress { get; set; } = 0;
    
    public string? Description { get; set; }
    
    public string? DrawingFileUrl { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual ICollection<MaterialEntry> MaterialEntries { get; set; } = new List<MaterialEntry>();
    public virtual ICollection<ProgressEntry> ProgressEntries { get; set; } = new List<ProgressEntry>();
}
