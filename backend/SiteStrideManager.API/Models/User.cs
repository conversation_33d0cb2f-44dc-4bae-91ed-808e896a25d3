using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.Models;

public class User
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string PasswordHash { get; set; } = string.Empty;
    
    [Required]
    public string FirmId { get; set; } = string.Empty;

    [Required]
    public string FirmName { get; set; } = string.Empty;

    public string? FirmLogoUrl { get; set; }

    [Required]
    public string Address { get; set; } = string.Empty;

    [Required]
    public string Contact { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string UserType { get; set; } = "FirmAdmin"; // FirmAdmin, FirmUser
    
    public bool IsActive { get; set; } = true;
    
    public bool IsFirstLogin { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? LastLoginAt { get; set; }

    // Navigation properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
    public virtual ICollection<PersonalExpense> PersonalExpenses { get; set; } = new List<PersonalExpense>();
    public virtual ICollection<RefreshToken> RefreshTokens { get; set; } = new List<RefreshToken>();
}
