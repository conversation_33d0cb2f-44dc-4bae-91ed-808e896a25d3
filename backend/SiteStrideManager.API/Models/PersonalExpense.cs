using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.Models;

public class PersonalExpense
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [Required]
    public string Description { get; set; } = string.Empty;
    
    public decimal Amount { get; set; }
    
    [Required]
    public string Category { get; set; } = string.Empty;
    
    public DateTime ExpenseDate { get; set; }
    
    public string? Notes { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual User User { get; set; } = null!;
}
