using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.Models;

public class ProgressEntry
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string SiteId { get; set; } = string.Empty;
    
    [Required]
    public string Milestone { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    public decimal CompletionPercentage { get; set; }
    
    public DateTime DateCompleted { get; set; }
    
    public string? Notes { get; set; }
    
    public string? PhotoUrls { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Site Site { get; set; } = null!;
}
