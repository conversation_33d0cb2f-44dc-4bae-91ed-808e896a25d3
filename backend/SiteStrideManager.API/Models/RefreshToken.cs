using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.Models;

public class RefreshToken
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [Required]
    public string Token { get; set; } = string.Empty;
    
    public DateTime ExpiresAt { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsRevoked { get; set; } = false;

    // Navigation properties
    public virtual User User { get; set; } = null!;
}
