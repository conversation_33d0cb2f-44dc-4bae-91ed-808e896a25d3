using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.Models;

public class SuperAdmin
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    [EmailAddress]
    [StringLength(255)]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string PasswordHash { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [StringLength(50)]
    public string? Phone { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public bool IsFirstLogin { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? LastLoginAt { get; set; }
    
    [StringLength(100)]
    public string Role { get; set; } = "SuperAdmin";
    
    // Navigation properties
    public virtual ICollection<SuperAdminAuditLog> AuditLogs { get; set; } = new List<SuperAdminAuditLog>();
}

public class SuperAdminAuditLog
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string SuperAdminId { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string Action { get; set; } = string.Empty; // CREATE_FIRM, UPDATE_FIRM, DELETE_FIRM, etc.
    
    [StringLength(500)]
    public string? Description { get; set; }
    
    [StringLength(50)]
    public string? TargetType { get; set; } // Firm, User, etc.
    
    [StringLength(100)]
    public string? TargetId { get; set; }
    
    public string? OldValues { get; set; } // JSON string of old values
    
    public string? NewValues { get; set; } // JSON string of new values
    
    [StringLength(45)]
    public string? IpAddress { get; set; }
    
    [StringLength(500)]
    public string? UserAgent { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual SuperAdmin SuperAdmin { get; set; } = null!;
}
