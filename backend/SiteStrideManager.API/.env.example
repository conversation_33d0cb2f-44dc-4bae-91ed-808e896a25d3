# Database Configuration
# Replace with your actual database connection strings
DB_DEFAULT_CONNECTION=Server=localhost,1433;Database=SiteStrideManager;User Id=sa;Password=YourPassword;TrustServerCertificate=true;
DB_SUPERADMIN_CONNECTION=Server=localhost,1433;Database=SiteStrideManagerSuperAdmin;User Id=sa;Password=YourPassword;TrustServerCertificate=true;

# JWT Configuration
# Generate a secure secret key (at least 32 characters)
JWT_SECRET_KEY=YourSuperSecretKeyThatIsAtLeast32CharactersLongAndSecure!
JWT_ISSUER=SiteStrideManager
JWT_AUDIENCE=SiteStrideManager
JWT_EXPIRY_MINUTES=60

# SuperAdmin Initial Setup
# These are used to create the initial SuperAdmin user on first startup
SUPERADMIN_EMAIL=<EMAIL>
SUPERADMIN_PASSWORD=YourSecurePassword123!
SUPERADMIN_FIRST_NAME=Super
SUPERADMIN_LAST_NAME=Admin
SUPERADMIN_PHONE=******-0123

# Application Configuration
APPLICATION_BASE_URL=http://localhost:8080

# Logging Configuration (optional)
LOGGING_LEVEL=Information

# CORS Configuration (optional)
CORS_ORIGINS=http://localhost:8080,http://localhost:8081,http://localhost:3000,http://localhost:5173
