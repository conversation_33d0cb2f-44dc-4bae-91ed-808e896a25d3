using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Services;

namespace SiteStrideManager.API.Controllers;

[ApiController]
[Route("api/superadmin/firms")]
[Authorize(Roles = "SuperAdmin")]
public class SuperAdminFirmController : ControllerBase
{
    private readonly IFirmManagementService _firmManagementService;

    public SuperAdminFirmController(IFirmManagementService firmManagementService)
    {
        _firmManagementService = firmManagementService;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllFirms()
    {
        var firms = await _firmManagementService.GetAllFirmsAsync();
        return Ok(firms);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetFirm(string id)
    {
        var firm = await _firmManagementService.GetFirmByIdAsync(id);
        if (firm == null)
            return NotFound(new { message = "Firm not found" });

        return Ok(firm);
    }

    [HttpPost]
    public async Task<IActionResult> CreateFirm([FromBody] CreateFirmRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var firm = await _firmManagementService.CreateFirmAsync(request);
        return CreatedAtAction(nameof(GetFirm), new { id = firm.Id }, firm);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateFirm(string id, [FromBody] UpdateFirmRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var firm = await _firmManagementService.UpdateFirmAsync(id, request);
        if (firm == null)
            return NotFound(new { message = "Firm not found" });

        return Ok(firm);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteFirm(string id)
    {
        var result = await _firmManagementService.DeleteFirmAsync(id);
        if (!result)
            return NotFound(new { message = "Firm not found" });

        return NoContent();
    }

    [HttpPost("{id}/activate")]
    public async Task<IActionResult> ActivateFirm(string id)
    {
        var result = await _firmManagementService.ActivateFirmAsync(id);
        if (!result)
            return NotFound(new { message = "Firm not found" });

        return Ok(new { message = "Firm activated successfully" });
    }

    [HttpPost("{id}/deactivate")]
    public async Task<IActionResult> DeactivateFirm(string id)
    {
        var result = await _firmManagementService.DeactivateFirmAsync(id);
        if (!result)
            return NotFound(new { message = "Firm not found" });

        return Ok(new { message = "Firm deactivated successfully" });
    }

    [HttpGet("{id}/users")]
    public async Task<IActionResult> GetFirmUsers(string id)
    {
        var users = await _firmManagementService.GetFirmUsersAsync(id);
        return Ok(users);
    }

    [HttpPost("{id}/admin")]
    public async Task<IActionResult> CreateFirmAdmin(string id, [FromBody] CreateFirmAdminRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        // Ensure the firm ID matches
        request.FirmId = id;

        try
        {
            var credentials = await _firmManagementService.CreateFirmAdminAsync(request);
            return Ok(credentials);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("users/{userId}/deactivate")]
    public async Task<IActionResult> DeactivateFirmUser(string userId)
    {
        var result = await _firmManagementService.DeactivateFirmUserAsync(userId);
        if (!result)
            return NotFound(new { message = "User not found" });

        return Ok(new { message = "User deactivated successfully" });
    }

    [HttpGet("dashboard/stats")]
    public async Task<IActionResult> GetDashboardStats()
    {
        var stats = await _firmManagementService.GetDashboardStatsAsync();
        return Ok(stats);
    }
}
