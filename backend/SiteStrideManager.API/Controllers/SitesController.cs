using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Services;
using System.Security.Claims;

namespace SiteStrideManager.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SitesController : ControllerBase
{
    private readonly ISiteService _siteService;

    public SitesController(ISiteService siteService)
    {
        _siteService = siteService;
    }

    private string GetUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
    }

    [HttpGet]
    public async Task<IActionResult> GetSites()
    {
        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var sites = await _siteService.GetSitesByUserIdAsync(userId);
        return Ok(sites);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetSite(string id)
    {
        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var site = await _siteService.GetSiteByIdAsync(id, userId);
        if (site == null)
            return NotFound(new { message = "Site not found" });

        return Ok(site);
    }

    [HttpPost]
    public async Task<IActionResult> CreateSite([FromBody] CreateSiteRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var site = await _siteService.CreateSiteAsync(userId, request);
        return CreatedAtAction(nameof(GetSite), new { id = site.Id }, site);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateSite(string id, [FromBody] UpdateSiteRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var site = await _siteService.UpdateSiteAsync(id, userId, request);
        if (site == null)
            return NotFound(new { message = "Site not found" });

        return Ok(site);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteSite(string id)
    {
        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var success = await _siteService.DeleteSiteAsync(id, userId);
        if (!success)
            return NotFound(new { message = "Site not found" });

        return NoContent();
    }

    [HttpGet("{siteId}/materials")]
    public async Task<IActionResult> GetMaterialEntries(string siteId)
    {
        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var entries = await _siteService.GetMaterialEntriesBySiteIdAsync(siteId, userId);
        return Ok(entries);
    }

    [HttpPost("materials")]
    public async Task<IActionResult> CreateMaterialEntry([FromBody] CreateMaterialEntryRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        try
        {
            var entry = await _siteService.CreateMaterialEntryAsync(userId, request);
            return Ok(entry);
        }
        catch (UnauthorizedAccessException)
        {
            return NotFound(new { message = "Site not found" });
        }
    }

    [HttpGet("{siteId}/progress")]
    public async Task<IActionResult> GetProgressEntries(string siteId)
    {
        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var entries = await _siteService.GetProgressEntriesBySiteIdAsync(siteId, userId);
        return Ok(entries);
    }

    [HttpPost("progress")]
    public async Task<IActionResult> CreateProgressEntry([FromBody] CreateProgressEntryRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        try
        {
            var entry = await _siteService.CreateProgressEntryAsync(userId, request);
            return Ok(entry);
        }
        catch (UnauthorizedAccessException)
        {
            return NotFound(new { message = "Site not found" });
        }
    }
}
