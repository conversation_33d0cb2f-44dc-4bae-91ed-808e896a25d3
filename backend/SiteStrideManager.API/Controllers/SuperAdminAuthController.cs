using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Services;
using System.Security.Claims;

namespace SiteStrideManager.API.Controllers;

[ApiController]
[Route("api/superadmin/auth")]
public class SuperAdminAuthController : ControllerBase
{
    private readonly ISuperAdminAuthService _superAdminAuthService;

    public SuperAdminAuthController(ISuperAdminAuthService superAdminAuthService)
    {
        _superAdminAuthService = superAdminAuthService;
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] SuperAdminLoginRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var result = await _superAdminAuthService.LoginAsync(request);
        if (result == null)
            return Unauthorized(new { message = "Invalid email or password" });

        return Ok(result);
    }

    [HttpPost("create")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<IActionResult> CreateSuperAdmin([FromBody] CreateSuperAdminRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var result = await _superAdminAuthService.CreateSuperAdminAsync(request);
        if (!result)
            return BadRequest(new { message = "Super admin with this email already exists" });

        return Ok(new { message = "Super admin created successfully" });
    }

    [HttpPost("change-password")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<IActionResult> ChangePassword([FromBody] SuperAdminChangePasswordRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var superAdminId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(superAdminId))
            return Unauthorized();

        var result = await _superAdminAuthService.ChangePasswordAsync(superAdminId, request);
        if (!result)
            return BadRequest(new { message = "Current password is incorrect" });

        return Ok(new { message = "Password changed successfully" });
    }

    [HttpPut("profile")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<IActionResult> UpdateProfile([FromBody] UpdateSuperAdminRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var superAdminId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(superAdminId))
            return Unauthorized();

        var result = await _superAdminAuthService.UpdateProfileAsync(superAdminId, request);
        if (result == null)
            return NotFound(new { message = "Super admin not found" });

        return Ok(result);
    }

    [HttpGet("me")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<IActionResult> GetCurrentSuperAdmin()
    {
        var superAdminId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(superAdminId))
            return Unauthorized();

        var superAdmin = await _superAdminAuthService.GetSuperAdminByIdAsync(superAdminId);
        if (superAdmin == null)
            return NotFound(new { message = "Super admin not found" });

        return Ok(superAdmin);
    }

    [HttpGet("all")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<IActionResult> GetAllSuperAdmins()
    {
        var superAdmins = await _superAdminAuthService.GetAllSuperAdminsAsync();
        return Ok(superAdmins);
    }

    [HttpPost("deactivate/{id}")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<IActionResult> DeactivateSuperAdmin(string id)
    {
        var result = await _superAdminAuthService.DeactivateSuperAdminAsync(id);
        if (!result)
            return NotFound(new { message = "Super admin not found" });

        return Ok(new { message = "Super admin deactivated successfully" });
    }

    [HttpPost("logout")]
    [Authorize(Roles = "SuperAdmin")]
    public IActionResult Logout()
    {
        // For JWT tokens, logout is handled client-side by removing the token
        return Ok(new { message = "Logged out successfully" });
    }
}
