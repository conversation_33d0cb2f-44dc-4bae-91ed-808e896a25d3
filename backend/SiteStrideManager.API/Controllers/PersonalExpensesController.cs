using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Services;
using System.Security.Claims;

namespace SiteStrideManager.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PersonalExpensesController : ControllerBase
{
    private readonly IPersonalExpenseService _expenseService;

    public PersonalExpensesController(IPersonalExpenseService expenseService)
    {
        _expenseService = expenseService;
    }

    private string GetUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
    }

    [HttpGet]
    public async Task<IActionResult> GetExpenses()
    {
        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var expenses = await _expenseService.GetExpensesByUserIdAsync(userId);
        return Ok(expenses);
    }

    [HttpPost]
    public async Task<IActionResult> CreateExpense([FromBody] CreatePersonalExpenseRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var expense = await _expenseService.CreateExpenseAsync(userId, request);
        return Ok(expense);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateExpense(string id, [FromBody] UpdatePersonalExpenseRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var expense = await _expenseService.UpdateExpenseAsync(id, userId, request);
        if (expense == null)
            return NotFound(new { message = "Expense not found" });

        return Ok(expense);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteExpense(string id)
    {
        var userId = GetUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var success = await _expenseService.DeleteExpenseAsync(id, userId);
        if (!success)
            return NotFound(new { message = "Expense not found" });

        return NoContent();
    }
}
