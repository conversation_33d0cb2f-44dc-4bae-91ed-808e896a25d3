using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.DTOs;

public class PersonalExpenseDto
{
    public string Id { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Category { get; set; } = string.Empty;
    public string Date { get; set; } = string.Empty;
    public string CreatedAt { get; set; } = string.Empty;
}

public class CreatePersonalExpenseRequestDto
{
    [Required]
    public string Description { get; set; } = string.Empty;
    
    public decimal Amount { get; set; }
    
    [Required]
    public string Category { get; set; } = string.Empty;
    
    [Required]
    public string Date { get; set; } = string.Empty;
}

public class UpdatePersonalExpenseRequestDto
{
    public string? Description { get; set; }
    public decimal? Amount { get; set; }
    public string? Category { get; set; }
    public string? Date { get; set; }
}
