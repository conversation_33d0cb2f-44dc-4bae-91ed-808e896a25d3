using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.DTOs;

public class SuperAdminDto
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public bool IsActive { get; set; }
    public bool IsFirstLogin { get; set; }
    public string CreatedAt { get; set; } = string.Empty;
    public string? LastLoginAt { get; set; }
    public string Role { get; set; } = string.Empty;
}

public class SuperAdminLoginRequestDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string Password { get; set; } = string.Empty;
}

public class SuperAdminAuthResponseDto
{
    public string Token { get; set; } = string.Empty;
    public SuperAdminDto SuperAdmin { get; set; } = new();
}

public class CreateSuperAdminRequestDto
{
    [Required]
    [EmailAddress]
    [StringLength(255)]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [StringLength(50)]
    public string? Phone { get; set; }
    
    [Required]
    [MinLength(8)]
    public string Password { get; set; } = string.Empty;
}

public class UpdateSuperAdminRequestDto
{
    [StringLength(100)]
    public string? FirstName { get; set; }
    
    [StringLength(100)]
    public string? LastName { get; set; }
    
    [StringLength(50)]
    public string? Phone { get; set; }
    
    public bool? IsActive { get; set; }
}

public class SuperAdminChangePasswordRequestDto
{
    [Required]
    public string CurrentPassword { get; set; } = string.Empty;
    
    [Required]
    [MinLength(8)]
    public string NewPassword { get; set; } = string.Empty;
}

public class SuperAdminAuditLogDto
{
    public string Id { get; set; } = string.Empty;
    public string SuperAdminId { get; set; } = string.Empty;
    public string SuperAdminName { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? TargetType { get; set; }
    public string? TargetId { get; set; }
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string CreatedAt { get; set; } = string.Empty;
}

public class DashboardStatsDto
{
    public int TotalFirms { get; set; }
    public int ActiveFirms { get; set; }
    public int InactiveFirms { get; set; }
    public int TotalUsers { get; set; }
    public int TotalSites { get; set; }
    public decimal TotalRevenue { get; set; }
    public int NewFirmsThisMonth { get; set; }
    public int NewUsersThisMonth { get; set; }
    public List<MonthlyStatsDto> MonthlyStats { get; set; } = new();
}

public class MonthlyStatsDto
{
    public string Month { get; set; } = string.Empty;
    public int NewFirms { get; set; }
    public int NewUsers { get; set; }
    public decimal Revenue { get; set; }
}
