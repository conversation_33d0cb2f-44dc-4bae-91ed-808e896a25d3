using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.DTOs;

public class FirmDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Address { get; set; } = string.Empty;
    public string Contact { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? LogoUrl { get; set; }
    public string? Website { get; set; }
    public string? TaxId { get; set; }
    public string? RegistrationNumber { get; set; }
    public bool IsActive { get; set; }
    public string CreatedAt { get; set; } = string.Empty;
    public string UpdatedAt { get; set; } = string.Empty;
    public string SubscriptionStatus { get; set; } = string.Empty;
    public string? SubscriptionExpiryDate { get; set; }
    public int MaxUsers { get; set; }
    public int MaxSites { get; set; }
    public int CurrentUserCount { get; set; }
    public int CurrentSiteCount { get; set; }
}

public class CreateFirmRequestDto
{
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string? Description { get; set; }
    
    [Required]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;
    
    [Required]
    [StringLength(50)]
    public string Contact { get; set; } = string.Empty;
    
    [EmailAddress]
    [StringLength(255)]
    public string? Email { get; set; }
    
    [StringLength(500)]
    public string? LogoUrl { get; set; }
    
    [StringLength(100)]
    public string? Website { get; set; }
    
    [StringLength(50)]
    public string? TaxId { get; set; }
    
    [StringLength(50)]
    public string? RegistrationNumber { get; set; }
    
    public int MaxUsers { get; set; } = 10;
    
    public int MaxSites { get; set; } = 50;
    
    public DateTime? SubscriptionExpiryDate { get; set; }
}

public class UpdateFirmRequestDto
{
    [StringLength(200)]
    public string? Name { get; set; }
    
    [StringLength(500)]
    public string? Description { get; set; }
    
    [StringLength(500)]
    public string? Address { get; set; }
    
    [StringLength(50)]
    public string? Contact { get; set; }
    
    [EmailAddress]
    [StringLength(255)]
    public string? Email { get; set; }
    
    [StringLength(500)]
    public string? LogoUrl { get; set; }
    
    [StringLength(100)]
    public string? Website { get; set; }
    
    [StringLength(50)]
    public string? TaxId { get; set; }
    
    [StringLength(50)]
    public string? RegistrationNumber { get; set; }
    
    public bool? IsActive { get; set; }
    
    public string? SubscriptionStatus { get; set; }
    
    public DateTime? SubscriptionExpiryDate { get; set; }
    
    public int? MaxUsers { get; set; }
    
    public int? MaxSites { get; set; }
}

public class FirmUserDto
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirmName { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Contact { get; set; } = string.Empty;
    public string UserType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsFirstLogin { get; set; }
    public string CreatedAt { get; set; } = string.Empty;
    public string? LastLoginAt { get; set; }
}

public class CreateFirmAdminRequestDto
{
    public string FirmId { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;
    
    [Required]
    [StringLength(50)]
    public string Contact { get; set; } = string.Empty;
    
    public string UserType { get; set; } = "FirmAdmin";
}

public class FirmCredentialsDto
{
    public string Email { get; set; } = string.Empty;
    public string TemporaryPassword { get; set; } = string.Empty;
    public string FirmName { get; set; } = string.Empty;
    public string LoginUrl { get; set; } = string.Empty;
}
