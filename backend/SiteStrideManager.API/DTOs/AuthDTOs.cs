using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.DTOs;

public class LoginRequestDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string Password { get; set; } = string.Empty;
}

public class RegisterRequestDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string Password { get; set; } = string.Empty;
    
    [Required]
    public string FirmName { get; set; } = string.Empty;
    
    public string? FirmLogo { get; set; }
    
    [Required]
    public string Address { get; set; } = string.Empty;
    
    [Required]
    public string Contact { get; set; } = string.Empty;
}

public class ChangePasswordRequestDto
{
    [Required]
    public string CurrentPassword { get; set; } = string.Empty;
    
    [Required]
    public string NewPassword { get; set; } = string.Empty;
}

public class UpdateProfileRequestDto
{
    public string? FirmName { get; set; }
    public string? FirmLogo { get; set; }
    public string? Address { get; set; }
    public string? Contact { get; set; }
}

public class AuthResponseDto
{
    public string Token { get; set; } = string.Empty;
    public UserDto User { get; set; } = null!;
}

public class UserDto
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirmId { get; set; } = string.Empty;
    public string FirmName { get; set; } = string.Empty;
    public string? FirmLogo { get; set; }
    public string Address { get; set; } = string.Empty;
    public string Contact { get; set; } = string.Empty;
    public string UserType { get; set; } = string.Empty;
    public bool? IsFirstLogin { get; set; }
}
