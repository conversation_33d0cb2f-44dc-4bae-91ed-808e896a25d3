using System.ComponentModel.DataAnnotations;

namespace SiteStrideManager.API.DTOs;

public class SiteDto
{
    public string Id { get; set; } = string.Empty;
    public string SiteName { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public decimal Area { get; set; }
    public string AreaUnit { get; set; } = string.Empty;
    public string SiteType { get; set; } = string.Empty;
    public string? DrawingFile { get; set; }
    public string CreatedAt { get; set; } = string.Empty;
    public decimal? EstimatedCost { get; set; }
    public decimal? ActualCost { get; set; }
    public decimal? Progress { get; set; }
}

public class CreateSiteRequestDto
{
    [Required]
    public string SiteName { get; set; } = string.Empty;
    
    [Required]
    public string Location { get; set; } = string.Empty;
    
    public decimal Area { get; set; }
    
    [Required]
    public string AreaUnit { get; set; } = string.Empty;
    
    [Required]
    public string SiteType { get; set; } = string.Empty;
    
    public string? DrawingFile { get; set; }
    public decimal? EstimatedCost { get; set; }
}

public class UpdateSiteRequestDto
{
    public string? SiteName { get; set; }
    public string? Location { get; set; }
    public decimal? Area { get; set; }
    public string? AreaUnit { get; set; }
    public string? SiteType { get; set; }
    public string? DrawingFile { get; set; }
    public decimal? EstimatedCost { get; set; }
    public decimal? ActualCost { get; set; }
    public decimal? Progress { get; set; }
}

public class MaterialEntryDto
{
    public string Id { get; set; } = string.Empty;
    public string SiteId { get; set; } = string.Empty;
    public string MaterialName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal Price { get; set; }
    public string PurchaseDate { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class CreateMaterialEntryRequestDto
{
    [Required]
    public string SiteId { get; set; } = string.Empty;
    
    [Required]
    public string MaterialName { get; set; } = string.Empty;
    
    public decimal Quantity { get; set; }
    
    public decimal Price { get; set; }
    
    [Required]
    public string PurchaseDate { get; set; } = string.Empty;
    
    public string? Notes { get; set; }
}

public class ProgressEntryDto
{
    public string Id { get; set; } = string.Empty;
    public string SiteId { get; set; } = string.Empty;
    public string Date { get; set; } = string.Empty;
    public string[] Photos { get; set; } = Array.Empty<string>();
    public string Notes { get; set; } = string.Empty;
}

public class CreateProgressEntryRequestDto
{
    [Required]
    public string SiteId { get; set; } = string.Empty;
    
    [Required]
    public string Date { get; set; } = string.Empty;
    
    public string[] Photos { get; set; } = Array.Empty<string>();
    
    [Required]
    public string Notes { get; set; } = string.Empty;
}
