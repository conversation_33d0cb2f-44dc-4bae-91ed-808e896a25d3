using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Data;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Models;

namespace SiteStrideManager.API.Services;

public class SiteService : ISiteService
{
    private readonly ApplicationDbContext _context;
    private readonly ITenantContextService _tenantContextService;

    public SiteService(ApplicationDbContext context, ITenantContextService tenantContextService)
    {
        _context = context;
        _tenantContextService = tenantContextService;
    }

    public async Task<IEnumerable<SiteDto>> GetSitesByUserIdAsync(string userId)
    {
        var sites = await _context.Sites
            .Where(s => s.UserId == userId)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return sites.Select(s => new SiteDto
        {
            Id = s.Id,
            SiteName = s.SiteName,
            Location = s.Location,
            Area = s.Area,
            AreaUnit = s.AreaUnit,
            SiteType = s.SiteType,
            DrawingFile = s.DrawingFileUrl,
            CreatedAt = s.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            EstimatedCost = s.EstimatedCost,
            ActualCost = s.ActualCost,
            Progress = s.Progress
        });
    }

    public async Task<SiteDto?> GetSiteByIdAsync(string siteId, string userId)
    {
        // Validate tenant access
        if (!await _tenantContextService.ValidateUserCanAccessSiteAsync(siteId, userId))
            return null;

        var site = await _context.Sites
            .FirstOrDefaultAsync(s => s.Id == siteId && s.UserId == userId);

        if (site == null)
            return null;

        return new SiteDto
        {
            Id = site.Id,
            SiteName = site.SiteName,
            Location = site.Location,
            Area = site.Area,
            AreaUnit = site.AreaUnit,
            SiteType = site.SiteType,
            DrawingFile = site.DrawingFileUrl,
            CreatedAt = site.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            EstimatedCost = site.EstimatedCost,
            ActualCost = site.ActualCost,
            Progress = site.Progress
        };
    }

    public async Task<SiteDto> CreateSiteAsync(string userId, CreateSiteRequestDto request)
    {
        var site = new Site
        {
            UserId = userId,
            SiteName = request.SiteName,
            Location = request.Location,
            Area = request.Area,
            AreaUnit = request.AreaUnit,
            SiteType = request.SiteType,
            DrawingFileUrl = request.DrawingFile,
            EstimatedCost = request.EstimatedCost,
            ActualCost = 0,
            Progress = 0
        };

        _context.Sites.Add(site);
        await _context.SaveChangesAsync();

        return new SiteDto
        {
            Id = site.Id,
            SiteName = site.SiteName,
            Location = site.Location,
            Area = site.Area,
            AreaUnit = site.AreaUnit,
            SiteType = site.SiteType,
            DrawingFile = site.DrawingFileUrl,
            CreatedAt = site.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            EstimatedCost = site.EstimatedCost,
            ActualCost = site.ActualCost,
            Progress = site.Progress
        };
    }

    public async Task<SiteDto?> UpdateSiteAsync(string siteId, string userId, UpdateSiteRequestDto request)
    {
        // Validate tenant access
        if (!await _tenantContextService.ValidateUserCanAccessSiteAsync(siteId, userId))
            return null;

        var site = await _context.Sites
            .FirstOrDefaultAsync(s => s.Id == siteId && s.UserId == userId);

        if (site == null)
            return null;

        if (!string.IsNullOrEmpty(request.SiteName))
            site.SiteName = request.SiteName;

        if (!string.IsNullOrEmpty(request.Location))
            site.Location = request.Location;

        if (request.Area.HasValue)
            site.Area = request.Area.Value;

        if (!string.IsNullOrEmpty(request.AreaUnit))
            site.AreaUnit = request.AreaUnit;

        if (!string.IsNullOrEmpty(request.SiteType))
            site.SiteType = request.SiteType;

        if (request.DrawingFile != null)
            site.DrawingFileUrl = request.DrawingFile;

        if (request.EstimatedCost.HasValue)
            site.EstimatedCost = request.EstimatedCost;

        if (request.ActualCost.HasValue)
            site.ActualCost = request.ActualCost.Value;

        if (request.Progress.HasValue)
            site.Progress = request.Progress.Value;

        site.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        return new SiteDto
        {
            Id = site.Id,
            SiteName = site.SiteName,
            Location = site.Location,
            Area = site.Area,
            AreaUnit = site.AreaUnit,
            SiteType = site.SiteType,
            DrawingFile = site.DrawingFileUrl,
            CreatedAt = site.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            EstimatedCost = site.EstimatedCost,
            ActualCost = site.ActualCost,
            Progress = site.Progress
        };
    }

    public async Task<bool> DeleteSiteAsync(string siteId, string userId)
    {
        // Validate tenant access
        if (!await _tenantContextService.ValidateUserCanAccessSiteAsync(siteId, userId))
            return false;

        var site = await _context.Sites
            .FirstOrDefaultAsync(s => s.Id == siteId && s.UserId == userId);

        if (site == null)
            return false;

        _context.Sites.Remove(site);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<MaterialEntryDto>> GetMaterialEntriesBySiteIdAsync(string siteId, string userId)
    {
        // Verify user owns the site
        var siteExists = await _context.Sites
            .AnyAsync(s => s.Id == siteId && s.UserId == userId);

        if (!siteExists)
            return Enumerable.Empty<MaterialEntryDto>();

        var entries = await _context.MaterialEntries
            .Where(m => m.SiteId == siteId)
            .OrderByDescending(m => m.PurchaseDate)
            .ToListAsync();

        return entries.Select(m => new MaterialEntryDto
        {
            Id = m.Id,
            SiteId = m.SiteId,
            MaterialName = m.MaterialName,
            Quantity = m.Quantity,
            Price = m.PricePerUnit * m.Quantity,
            PurchaseDate = m.PurchaseDate.ToString("yyyy-MM-dd"),
            Notes = m.Notes
        });
    }

    public async Task<MaterialEntryDto> CreateMaterialEntryAsync(string userId, CreateMaterialEntryRequestDto request)
    {
        // Verify user owns the site
        var siteExists = await _context.Sites
            .AnyAsync(s => s.Id == request.SiteId && s.UserId == userId);

        if (!siteExists)
            throw new UnauthorizedAccessException("Site not found or access denied");

        var entry = new MaterialEntry
        {
            SiteId = request.SiteId,
            MaterialName = request.MaterialName,
            Quantity = request.Quantity,
            Unit = "unit", // Default unit
            PricePerUnit = request.Quantity > 0 ? request.Price / request.Quantity : 0,
            PurchaseDate = DateTime.Parse(request.PurchaseDate),
            Notes = request.Notes
        };

        _context.MaterialEntries.Add(entry);
        await _context.SaveChangesAsync();

        return new MaterialEntryDto
        {
            Id = entry.Id,
            SiteId = entry.SiteId,
            MaterialName = entry.MaterialName,
            Quantity = entry.Quantity,
            Price = request.Price,
            PurchaseDate = request.PurchaseDate,
            Notes = entry.Notes
        };
    }

    public async Task<IEnumerable<ProgressEntryDto>> GetProgressEntriesBySiteIdAsync(string siteId, string userId)
    {
        // Verify user owns the site
        var siteExists = await _context.Sites
            .AnyAsync(s => s.Id == siteId && s.UserId == userId);

        if (!siteExists)
            return Enumerable.Empty<ProgressEntryDto>();

        var entries = await _context.ProgressEntries
            .Where(p => p.SiteId == siteId)
            .OrderByDescending(p => p.DateCompleted)
            .ToListAsync();

        return entries.Select(p => new ProgressEntryDto
        {
            Id = p.Id,
            SiteId = p.SiteId,
            Date = p.DateCompleted.ToString("yyyy-MM-dd"),
            Photos = string.IsNullOrEmpty(p.PhotoUrls) ? Array.Empty<string>() : p.PhotoUrls.Split(','),
            Notes = p.Notes ?? string.Empty
        });
    }

    public async Task<ProgressEntryDto> CreateProgressEntryAsync(string userId, CreateProgressEntryRequestDto request)
    {
        // Verify user owns the site
        var siteExists = await _context.Sites
            .AnyAsync(s => s.Id == request.SiteId && s.UserId == userId);

        if (!siteExists)
            throw new UnauthorizedAccessException("Site not found or access denied");

        var entry = new ProgressEntry
        {
            SiteId = request.SiteId,
            Milestone = "Progress Update",
            Description = request.Notes,
            CompletionPercentage = 0,
            DateCompleted = DateTime.Parse(request.Date),
            Notes = request.Notes,
            PhotoUrls = request.Photos.Length > 0 ? string.Join(",", request.Photos) : null
        };

        _context.ProgressEntries.Add(entry);
        await _context.SaveChangesAsync();

        return new ProgressEntryDto
        {
            Id = entry.Id,
            SiteId = entry.SiteId,
            Date = request.Date,
            Photos = request.Photos,
            Notes = request.Notes
        };
    }
}
