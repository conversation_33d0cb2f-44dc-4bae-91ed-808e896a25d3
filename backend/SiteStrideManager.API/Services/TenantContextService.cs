using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Data;

namespace SiteStrideManager.API.Services;

public class TenantContextService : ITenantContextService
{
    private readonly ApplicationDbContext _context;
    private readonly SuperAdminDbContext _superAdminContext;

    public TenantContextService(ApplicationDbContext context, SuperAdminDbContext superAdminContext)
    {
        _context = context;
        _superAdminContext = superAdminContext;
    }

    public async Task<string?> GetUserFirmIdAsync(string userId)
    {
        var user = await _context.Users
            .Where(u => u.Id == userId && u.IsActive)
            .Select(u => u.FirmId)
            .FirstOrDefaultAsync();

        return user;
    }

    public async Task<bool> ValidateUserBelongsToFirmAsync(string userId, string firmId)
    {
        var userFirmId = await GetUserFirmIdAsync(userId);
        return userFirmId == firmId;
    }

    public async Task<bool> ValidateSiteBelongsToUserFirmAsync(string siteId, string userId)
    {
        var userFirmId = await GetUserFirmIdAsync(userId);
        if (userFirmId == null)
            return false;

        var site = await _context.Sites
            .Include(s => s.User)
            .Where(s => s.Id == siteId)
            .FirstOrDefaultAsync();

        if (site == null)
            return false;

        return site.User.FirmId == userFirmId;
    }

    public async Task<bool> ValidateUserCanAccessSiteAsync(string siteId, string userId)
    {
        // First check if the user owns the site
        var userOwnsSite = await _context.Sites
            .AnyAsync(s => s.Id == siteId && s.UserId == userId);

        if (userOwnsSite)
            return true;

        // If not, check if the site belongs to the same firm (for firm admins)
        return await ValidateSiteBelongsToUserFirmAsync(siteId, userId);
    }

    public async Task<IEnumerable<string>> GetFirmUserIdsAsync(string firmId)
    {
        var userIds = await _context.Users
            .Where(u => u.FirmId == firmId && u.IsActive)
            .Select(u => u.Id)
            .ToListAsync();

        return userIds;
    }

    public async Task<bool> IsFirmActiveAsync(string firmId)
    {
        var firm = await _superAdminContext.Firms
            .Where(f => f.Id == firmId)
            .Select(f => f.IsActive)
            .FirstOrDefaultAsync();

        return firm;
    }
}
