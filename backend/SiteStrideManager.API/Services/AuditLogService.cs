using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Data;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Models;
using System.Text.Json;

namespace SiteStrideManager.API.Services;

public class AuditLogService : IAuditLogService
{
    private readonly SuperAdminDbContext _context;

    public AuditLogService(SuperAdminDbContext context)
    {
        _context = context;
    }

    public async Task LogActionAsync(string superAdminId, string action, string? description = null,
        string? targetType = null, string? targetId = null,
        object? oldValues = null, object? newValues = null,
        string? ipAddress = null, string? userAgent = null)
    {
        var auditLog = new SuperAdminAuditLog
        {
            SuperAdminId = superAdminId,
            Action = action,
            Description = description,
            TargetType = targetType,
            TargetId = targetId,
            OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
            NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
            IpAddress = ipAddress,
            UserAgent = userAgent
        };

        _context.SuperAdminAuditLogs.Add(auditLog);
        await _context.SaveChangesAsync();
    }

    public async Task<IEnumerable<SuperAdminAuditLogDto>> GetAuditLogsAsync(int page = 1, int pageSize = 50)
    {
        var logs = await _context.SuperAdminAuditLogs
            .Include(al => al.SuperAdmin)
            .OrderByDescending(al => al.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return logs.Select(al => new SuperAdminAuditLogDto
        {
            Id = al.Id,
            SuperAdminId = al.SuperAdminId,
            SuperAdminName = $"{al.SuperAdmin.FirstName} {al.SuperAdmin.LastName}",
            Action = al.Action,
            Description = al.Description,
            TargetType = al.TargetType,
            TargetId = al.TargetId,
            OldValues = al.OldValues,
            NewValues = al.NewValues,
            IpAddress = al.IpAddress,
            UserAgent = al.UserAgent,
            CreatedAt = al.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        });
    }

    public async Task<IEnumerable<SuperAdminAuditLogDto>> GetAuditLogsByActionAsync(string action, int page = 1, int pageSize = 50)
    {
        var logs = await _context.SuperAdminAuditLogs
            .Include(al => al.SuperAdmin)
            .Where(al => al.Action == action)
            .OrderByDescending(al => al.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return logs.Select(al => new SuperAdminAuditLogDto
        {
            Id = al.Id,
            SuperAdminId = al.SuperAdminId,
            SuperAdminName = $"{al.SuperAdmin.FirstName} {al.SuperAdmin.LastName}",
            Action = al.Action,
            Description = al.Description,
            TargetType = al.TargetType,
            TargetId = al.TargetId,
            OldValues = al.OldValues,
            NewValues = al.NewValues,
            IpAddress = al.IpAddress,
            UserAgent = al.UserAgent,
            CreatedAt = al.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        });
    }

    public async Task<IEnumerable<SuperAdminAuditLogDto>> GetAuditLogsBySuperAdminAsync(string superAdminId, int page = 1, int pageSize = 50)
    {
        var logs = await _context.SuperAdminAuditLogs
            .Include(al => al.SuperAdmin)
            .Where(al => al.SuperAdminId == superAdminId)
            .OrderByDescending(al => al.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return logs.Select(al => new SuperAdminAuditLogDto
        {
            Id = al.Id,
            SuperAdminId = al.SuperAdminId,
            SuperAdminName = $"{al.SuperAdmin.FirstName} {al.SuperAdmin.LastName}",
            Action = al.Action,
            Description = al.Description,
            TargetType = al.TargetType,
            TargetId = al.TargetId,
            OldValues = al.OldValues,
            NewValues = al.NewValues,
            IpAddress = al.IpAddress,
            UserAgent = al.UserAgent,
            CreatedAt = al.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        });
    }
}
