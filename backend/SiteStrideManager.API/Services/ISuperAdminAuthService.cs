using SiteStrideManager.API.DTOs;

namespace SiteStrideManager.API.Services;

public interface ISuperAdminAuthService
{
    Task<SuperAdminAuthResponseDto?> LoginAsync(SuperAdminLoginRequestDto request);
    Task<bool> CreateSuperAdminAsync(CreateSuperAdminRequestDto request);
    Task<bool> ChangePasswordAsync(string superAdminId, SuperAdminChangePasswordRequestDto request);
    Task<SuperAdminDto?> UpdateProfileAsync(string superAdminId, UpdateSuperAdminRequestDto request);
    Task<SuperAdminDto?> GetSuperAdminByIdAsync(string superAdminId);
    Task<IEnumerable<SuperAdminDto>> GetAllSuperAdminsAsync();
    Task<bool> DeactivateSuperAdminAsync(string superAdminId);
}
