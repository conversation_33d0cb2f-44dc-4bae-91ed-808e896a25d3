using SiteStrideManager.API.DTOs;

namespace SiteStrideManager.API.Services;

public interface IAuthService
{
    Task<AuthResponseDto?> LoginAsync(LoginRequestDto request);
    Task<bool> RegisterAsync(RegisterRequestDto request);
    Task<bool> ChangePasswordAsync(string userId, ChangePasswordRequestDto request);
    Task<UserDto?> UpdateProfileAsync(string userId, UpdateProfileRequestDto request);
    Task<UserDto?> GetUserByIdAsync(string userId);
}
