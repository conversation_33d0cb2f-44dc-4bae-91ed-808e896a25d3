using SiteStrideManager.API.DTOs;

namespace SiteStrideManager.API.Services;

public interface IPersonalExpenseService
{
    Task<IEnumerable<PersonalExpenseDto>> GetExpensesByUserIdAsync(string userId);
    Task<PersonalExpenseDto> CreateExpenseAsync(string userId, CreatePersonalExpenseRequestDto request);
    Task<PersonalExpenseDto?> UpdateExpenseAsync(string expenseId, string userId, UpdatePersonalExpenseRequestDto request);
    Task<bool> DeleteExpenseAsync(string expenseId, string userId);
}
