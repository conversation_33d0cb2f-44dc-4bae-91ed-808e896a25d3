using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Data;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Models;

namespace SiteStrideManager.API.Services;

public class PersonalExpenseService : IPersonalExpenseService
{
    private readonly ApplicationDbContext _context;

    public PersonalExpenseService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<PersonalExpenseDto>> GetExpensesByUserIdAsync(string userId)
    {
        var expenses = await _context.PersonalExpenses
            .Where(e => e.UserId == userId)
            .OrderByDescending(e => e.ExpenseDate)
            .ToListAsync();

        return expenses.Select(e => new PersonalExpenseDto
        {
            Id = e.Id,
            Description = e.Description,
            Amount = e.Amount,
            Category = e.Category,
            Date = e.ExpenseDate.ToString("yyyy-MM-dd"),
            CreatedAt = e.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        });
    }

    public async Task<PersonalExpenseDto> CreateExpenseAsync(string userId, CreatePersonalExpenseRequestDto request)
    {
        var expense = new PersonalExpense
        {
            UserId = userId,
            Description = request.Description,
            Amount = request.Amount,
            Category = request.Category,
            ExpenseDate = DateTime.Parse(request.Date)
        };

        _context.PersonalExpenses.Add(expense);
        await _context.SaveChangesAsync();

        return new PersonalExpenseDto
        {
            Id = expense.Id,
            Description = expense.Description,
            Amount = expense.Amount,
            Category = expense.Category,
            Date = request.Date,
            CreatedAt = expense.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        };
    }

    public async Task<PersonalExpenseDto?> UpdateExpenseAsync(string expenseId, string userId, UpdatePersonalExpenseRequestDto request)
    {
        var expense = await _context.PersonalExpenses
            .FirstOrDefaultAsync(e => e.Id == expenseId && e.UserId == userId);

        if (expense == null)
            return null;

        if (!string.IsNullOrEmpty(request.Description))
            expense.Description = request.Description;
        
        if (request.Amount.HasValue)
            expense.Amount = request.Amount.Value;
        
        if (!string.IsNullOrEmpty(request.Category))
            expense.Category = request.Category;
        
        if (!string.IsNullOrEmpty(request.Date))
            expense.ExpenseDate = DateTime.Parse(request.Date);

        expense.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        return new PersonalExpenseDto
        {
            Id = expense.Id,
            Description = expense.Description,
            Amount = expense.Amount,
            Category = expense.Category,
            Date = expense.ExpenseDate.ToString("yyyy-MM-dd"),
            CreatedAt = expense.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        };
    }

    public async Task<bool> DeleteExpenseAsync(string expenseId, string userId)
    {
        var expense = await _context.PersonalExpenses
            .FirstOrDefaultAsync(e => e.Id == expenseId && e.UserId == userId);

        if (expense == null)
            return false;

        _context.PersonalExpenses.Remove(expense);
        await _context.SaveChangesAsync();
        return true;
    }
}
