using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Data;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Models;
using BCrypt.Net;

namespace SiteStrideManager.API.Services;

public class AuthService : IAuthService
{
    private readonly ApplicationDbContext _context;
    private readonly SuperAdminDbContext _superAdminContext;
    private readonly IJwtService _jwtService;

    public AuthService(ApplicationDbContext context, SuperAdminDbContext superAdminContext, IJwtService jwtService)
    {
        _context = context;
        _superAdminContext = superAdminContext;
        _jwtService = jwtService;
    }

    public async Task<AuthResponseDto?> LoginAsync(LoginRequestDto request)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == request.Email && u.IsActive);

        if (user == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
        {
            return null;
        }

        // Check if firm is active
        var firm = await _superAdminContext.Firms.FindAsync(user.FirmId);
        if (firm == null || !firm.IsActive)
        {
            return null;
        }

        // Update last login
        user.LastLoginAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        var token = _jwtService.GenerateToken(user);

        return new AuthResponseDto
        {
            Token = token,
            User = new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                FirmId = user.FirmId,
                FirmName = user.FirmName,
                FirmLogo = user.FirmLogoUrl,
                Address = user.Address,
                Contact = user.Contact,
                UserType = user.UserType,
                IsFirstLogin = user.IsFirstLogin
            }
        };
    }

    public async Task<bool> RegisterAsync(RegisterRequestDto request)
    {
        // Check if user already exists
        var existingUser = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == request.Email);

        if (existingUser != null)
        {
            return false;
        }

        var user = new User
        {
            Email = request.Email,
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
            FirmName = request.FirmName,
            FirmLogoUrl = request.FirmLogo,
            Address = request.Address,
            Contact = request.Contact,
            IsFirstLogin = true,
            IsActive = true
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> ChangePasswordAsync(string userId, ChangePasswordRequestDto request)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null || !BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.PasswordHash))
        {
            return false;
        }

        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
        user.IsFirstLogin = false;
        user.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<UserDto?> UpdateProfileAsync(string userId, UpdateProfileRequestDto request)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null)
        {
            return null;
        }

        if (!string.IsNullOrEmpty(request.FirmName))
            user.FirmName = request.FirmName;
        
        if (request.FirmLogo != null)
            user.FirmLogoUrl = request.FirmLogo;
        
        if (!string.IsNullOrEmpty(request.Address))
            user.Address = request.Address;
        
        if (!string.IsNullOrEmpty(request.Contact))
            user.Contact = request.Contact;

        user.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            FirmName = user.FirmName,
            FirmLogo = user.FirmLogoUrl,
            Address = user.Address,
            Contact = user.Contact,
            IsFirstLogin = user.IsFirstLogin
        };
    }

    public async Task<UserDto?> GetUserByIdAsync(string userId)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null)
        {
            return null;
        }

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            FirmId = user.FirmId,
            FirmName = user.FirmName,
            FirmLogo = user.FirmLogoUrl,
            Address = user.Address,
            Contact = user.Contact,
            UserType = user.UserType,
            IsFirstLogin = user.IsFirstLogin
        };
    }
}
