using SiteStrideManager.API.DTOs;

namespace SiteStrideManager.API.Services;

public interface IAuditLogService
{
    Task LogActionAsync(string superAdminId, string action, string? description = null, 
        string? targetType = null, string? targetId = null, 
        object? oldValues = null, object? newValues = null,
        string? ipAddress = null, string? userAgent = null);
    
    Task<IEnumerable<SuperAdminAuditLogDto>> GetAuditLogsAsync(int page = 1, int pageSize = 50);
    Task<IEnumerable<SuperAdminAuditLogDto>> GetAuditLogsByActionAsync(string action, int page = 1, int pageSize = 50);
    Task<IEnumerable<SuperAdminAuditLogDto>> GetAuditLogsBySuperAdminAsync(string superAdminId, int page = 1, int pageSize = 50);
}
