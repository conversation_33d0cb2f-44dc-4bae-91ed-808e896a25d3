namespace SiteStrideManager.API.Services;

public interface ITenantContextService
{
    Task<string?> GetUserFirmIdAsync(string userId);
    Task<bool> ValidateUserBelongsToFirmAsync(string userId, string firmId);
    Task<bool> ValidateSiteBelongsToUserFirmAsync(string siteId, string userId);
    Task<bool> ValidateUserCanAccessSiteAsync(string siteId, string userId);
    Task<IEnumerable<string>> GetFirmUserIdsAsync(string firmId);
    Task<bool> IsFirmActiveAsync(string firmId);
}
