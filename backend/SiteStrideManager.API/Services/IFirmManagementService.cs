using SiteStrideManager.API.DTOs;

namespace SiteStrideManager.API.Services;

public interface IFirmManagementService
{
    Task<IEnumerable<FirmDto>> GetAllFirmsAsync();
    Task<FirmDto?> GetFirmByIdAsync(string firmId);
    Task<FirmDto> CreateFirmAsync(CreateFirmRequestDto request);
    Task<FirmDto?> UpdateFirmAsync(string firmId, UpdateFirmRequestDto request);
    Task<bool> DeleteFirmAsync(string firmId);
    Task<bool> ActivateFirmAsync(string firmId);
    Task<bool> DeactivateFirmAsync(string firmId);
    Task<IEnumerable<FirmUserDto>> GetFirmUsersAsync(string firmId);
    Task<FirmCredentialsDto> CreateFirmAdminAsync(CreateFirmAdminRequestDto request);
    Task<bool> DeactivateFirmUserAsync(string userId);
    Task<DashboardStatsDto> GetDashboardStatsAsync();
}
