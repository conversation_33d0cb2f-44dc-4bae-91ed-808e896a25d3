using SiteStrideManager.API.DTOs;

namespace SiteStrideManager.API.Services;

public interface ISiteService
{
    Task<IEnumerable<SiteDto>> GetSitesByUserIdAsync(string userId);
    Task<SiteDto?> GetSiteByIdAsync(string siteId, string userId);
    Task<SiteDto> CreateSiteAsync(string userId, CreateSiteRequestDto request);
    Task<SiteDto?> UpdateSiteAsync(string siteId, string userId, UpdateSiteRequestDto request);
    Task<bool> DeleteSiteAsync(string siteId, string userId);
    
    Task<IEnumerable<MaterialEntryDto>> GetMaterialEntriesBySiteIdAsync(string siteId, string userId);
    Task<MaterialEntryDto> CreateMaterialEntryAsync(string userId, CreateMaterialEntryRequestDto request);
    
    Task<IEnumerable<ProgressEntryDto>> GetProgressEntriesBySiteIdAsync(string siteId, string userId);
    Task<ProgressEntryDto> CreateProgressEntryAsync(string userId, CreateProgressEntryRequestDto request);
}
