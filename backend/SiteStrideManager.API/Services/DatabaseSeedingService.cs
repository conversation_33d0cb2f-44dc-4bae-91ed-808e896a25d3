using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Data;
using SiteStrideManager.API.Models;

namespace SiteStrideManager.API.Services;

public interface IDatabaseSeedingService
{
    Task SeedInitialDataAsync();
}

public class DatabaseSeedingService : IDatabaseSeedingService
{
    private readonly SuperAdminDbContext _superAdminContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<DatabaseSeedingService> _logger;

    public DatabaseSeedingService(
        SuperAdminDbContext superAdminContext,
        IConfiguration configuration,
        ILogger<DatabaseSeedingService> logger)
    {
        _superAdminContext = superAdminContext;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task SeedInitialDataAsync()
    {
        try
        {
            // Ensure database is created
            await _superAdminContext.Database.EnsureCreatedAsync();

            // Seed initial SuperAdmin if none exists
            await SeedInitialSuperAdminAsync();

            _logger.LogInformation("Database seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during database seeding");
            throw;
        }
    }

    private async Task SeedInitialSuperAdminAsync()
    {
        // Check if any SuperAdmin exists
        var existingSuperAdmin = await _superAdminContext.SuperAdmins.AnyAsync();
        if (existingSuperAdmin)
        {
            _logger.LogInformation("SuperAdmin already exists, skipping initial seeding");
            return;
        }

        // Get SuperAdmin credentials from environment variables
        var email = GetRequiredEnvironmentVariable("SUPERADMIN_EMAIL");
        var password = GetRequiredEnvironmentVariable("SUPERADMIN_PASSWORD");
        var firstName = GetRequiredEnvironmentVariable("SUPERADMIN_FIRST_NAME");
        var lastName = GetRequiredEnvironmentVariable("SUPERADMIN_LAST_NAME");
        var phone = _configuration["SUPERADMIN_PHONE"]; // Optional

        // Create initial SuperAdmin
        var superAdmin = new SuperAdmin
        {
            Email = email,
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(password),
            FirstName = firstName,
            LastName = lastName,
            Phone = phone,
            IsActive = true,
            IsFirstLogin = true
        };

        _superAdminContext.SuperAdmins.Add(superAdmin);
        await _superAdminContext.SaveChangesAsync();

        _logger.LogInformation("Initial SuperAdmin created successfully with email: {Email}", email);
    }

    private string GetRequiredEnvironmentVariable(string key)
    {
        var value = _configuration[key];
        
        if (string.IsNullOrEmpty(value))
        {
            throw new InvalidOperationException($"Environment variable '{key}' is required but not set. Please configure it in your environment or appsettings.");
        }
        
        return value;
    }
}
