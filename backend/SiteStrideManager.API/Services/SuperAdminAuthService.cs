using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Data;
using SiteStrideManager.API.DTOs;
using SiteStrideManager.API.Models;
using BCrypt.Net;

namespace SiteStrideManager.API.Services;

public class SuperAdminAuthService : ISuperAdminAuthService
{
    private readonly SuperAdminDbContext _context;
    private readonly IJwtService _jwtService;

    public SuperAdminAuthService(SuperAdminDbContext context, IJwtService jwtService)
    {
        _context = context;
        _jwtService = jwtService;
    }

    public async Task<SuperAdminAuthResponseDto?> LoginAsync(SuperAdminLoginRequestDto request)
    {
        var superAdmin = await _context.SuperAdmins
            .FirstOrDefaultAsync(sa => sa.Email == request.Email && sa.IsActive);

        if (superAdmin == null || !BCrypt.Net.BCrypt.Verify(request.Password, superAdmin.PasswordHash))
        {
            return null;
        }

        // Update last login
        superAdmin.LastLoginAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        var token = _jwtService.GenerateSuperAdminToken(superAdmin);

        return new SuperAdminAuthResponseDto
        {
            Token = token,
            SuperAdmin = new SuperAdminDto
            {
                Id = superAdmin.Id,
                Email = superAdmin.Email,
                FirstName = superAdmin.FirstName,
                LastName = superAdmin.LastName,
                Phone = superAdmin.Phone,
                IsActive = superAdmin.IsActive,
                IsFirstLogin = superAdmin.IsFirstLogin,
                CreatedAt = superAdmin.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                LastLoginAt = superAdmin.LastLoginAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                Role = superAdmin.Role
            }
        };
    }

    public async Task<bool> CreateSuperAdminAsync(CreateSuperAdminRequestDto request)
    {
        // Check if super admin already exists
        var existingSuperAdmin = await _context.SuperAdmins
            .FirstOrDefaultAsync(sa => sa.Email == request.Email);

        if (existingSuperAdmin != null)
        {
            return false;
        }

        var superAdmin = new SuperAdmin
        {
            Email = request.Email,
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
            FirstName = request.FirstName,
            LastName = request.LastName,
            Phone = request.Phone,
            IsFirstLogin = true,
            IsActive = true
        };

        _context.SuperAdmins.Add(superAdmin);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> ChangePasswordAsync(string superAdminId, SuperAdminChangePasswordRequestDto request)
    {
        var superAdmin = await _context.SuperAdmins.FindAsync(superAdminId);
        if (superAdmin == null || !BCrypt.Net.BCrypt.Verify(request.CurrentPassword, superAdmin.PasswordHash))
        {
            return false;
        }

        superAdmin.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
        superAdmin.IsFirstLogin = false;
        superAdmin.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<SuperAdminDto?> UpdateProfileAsync(string superAdminId, UpdateSuperAdminRequestDto request)
    {
        var superAdmin = await _context.SuperAdmins.FindAsync(superAdminId);
        if (superAdmin == null)
        {
            return null;
        }

        if (!string.IsNullOrEmpty(request.FirstName))
            superAdmin.FirstName = request.FirstName;

        if (!string.IsNullOrEmpty(request.LastName))
            superAdmin.LastName = request.LastName;

        if (request.Phone != null)
            superAdmin.Phone = request.Phone;

        if (request.IsActive.HasValue)
            superAdmin.IsActive = request.IsActive.Value;

        superAdmin.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return new SuperAdminDto
        {
            Id = superAdmin.Id,
            Email = superAdmin.Email,
            FirstName = superAdmin.FirstName,
            LastName = superAdmin.LastName,
            Phone = superAdmin.Phone,
            IsActive = superAdmin.IsActive,
            IsFirstLogin = superAdmin.IsFirstLogin,
            CreatedAt = superAdmin.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            LastLoginAt = superAdmin.LastLoginAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            Role = superAdmin.Role
        };
    }

    public async Task<SuperAdminDto?> GetSuperAdminByIdAsync(string superAdminId)
    {
        var superAdmin = await _context.SuperAdmins.FindAsync(superAdminId);
        if (superAdmin == null)
        {
            return null;
        }

        return new SuperAdminDto
        {
            Id = superAdmin.Id,
            Email = superAdmin.Email,
            FirstName = superAdmin.FirstName,
            LastName = superAdmin.LastName,
            Phone = superAdmin.Phone,
            IsActive = superAdmin.IsActive,
            IsFirstLogin = superAdmin.IsFirstLogin,
            CreatedAt = superAdmin.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            LastLoginAt = superAdmin.LastLoginAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            Role = superAdmin.Role
        };
    }

    public async Task<IEnumerable<SuperAdminDto>> GetAllSuperAdminsAsync()
    {
        var superAdmins = await _context.SuperAdmins
            .OrderByDescending(sa => sa.CreatedAt)
            .ToListAsync();

        return superAdmins.Select(sa => new SuperAdminDto
        {
            Id = sa.Id,
            Email = sa.Email,
            FirstName = sa.FirstName,
            LastName = sa.LastName,
            Phone = sa.Phone,
            IsActive = sa.IsActive,
            IsFirstLogin = sa.IsFirstLogin,
            CreatedAt = sa.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            LastLoginAt = sa.LastLoginAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            Role = sa.Role
        });
    }

    public async Task<bool> DeactivateSuperAdminAsync(string superAdminId)
    {
        var superAdmin = await _context.SuperAdmins.FindAsync(superAdminId);
        if (superAdmin == null)
        {
            return false;
        }

        superAdmin.IsActive = false;
        superAdmin.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }
}
