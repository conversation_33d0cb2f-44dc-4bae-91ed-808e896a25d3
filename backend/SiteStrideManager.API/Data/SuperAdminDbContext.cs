using Microsoft.EntityFrameworkCore;
using SiteStrideManager.API.Models;

namespace SiteStrideManager.API.Data;

public class SuperAdminDbContext : DbContext
{
    public SuperAdminDbContext(DbContextOptions<SuperAdminDbContext> options) : base(options)
    {
    }

    public DbSet<SuperAdmin> SuperAdmins { get; set; }
    public DbSet<SuperAdminAuditLog> SuperAdminAuditLogs { get; set; }
    public DbSet<SuperAdminFirm> Firms { get; set; }
    public DbSet<SuperAdminFirmSubscription> FirmSubscriptions { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // This will be overridden by the DI configuration
            optionsBuilder.UseSqlServer();
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Firm configuration
        modelBuilder.Entity<SuperAdminFirm>(entity =>
        {
            entity.ToTable("Firms");
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Name);
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.SubscriptionStatus);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
        });

        // FirmSubscription configuration
        modelBuilder.Entity<SuperAdminFirmSubscription>(entity =>
        {
            entity.ToTable("FirmSubscriptions");
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.FirmId);
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => e.EndDate);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.MonthlyPrice).HasColumnType("decimal(10,2)");

            entity.HasOne(e => e.Firm)
                .WithMany(e => e.Subscriptions)
                .HasForeignKey(e => e.FirmId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // SuperAdmin configuration
        modelBuilder.Entity<SuperAdmin>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Email).IsUnique();
            entity.HasIndex(e => e.IsActive);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
        });

        // SuperAdminAuditLog configuration
        modelBuilder.Entity<SuperAdminAuditLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.SuperAdminId);
            entity.HasIndex(e => e.Action);
            entity.HasIndex(e => e.TargetType);
            entity.HasIndex(e => e.CreatedAt);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            entity.HasOne(e => e.SuperAdmin)
                .WithMany(e => e.AuditLogs)
                .HasForeignKey(e => e.SuperAdminId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }
}
