using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using SiteStrideManager.API.Data;
using SiteStrideManager.API.Services;
using SiteStrideManager.API.Middleware;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Load environment variables from .env file if it exists (for development)
var envPath = Path.Combine(Directory.GetCurrentDirectory(), ".env");
if (File.Exists(envPath))
{
    foreach (var line in File.ReadAllLines(envPath))
    {
        if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
            continue;

        var parts = line.Split('=', 2);
        if (parts.Length == 2)
        {
            Environment.SetEnvironmentVariable(parts[0].Trim(), parts[1].Trim());
        }
    }
}

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/sitestride-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Configure Entity Framework with environment variable support
var defaultConnection = Environment.GetEnvironmentVariable("DB_DEFAULT_CONNECTION")
    ?? builder.Configuration.GetConnectionString("DefaultConnection");
var superAdminConnection = Environment.GetEnvironmentVariable("DB_SUPERADMIN_CONNECTION")
    ?? builder.Configuration.GetConnectionString("SuperAdminConnection");

if (string.IsNullOrEmpty(defaultConnection))
    throw new InvalidOperationException("Database connection string 'DB_DEFAULT_CONNECTION' is required but not configured.");
if (string.IsNullOrEmpty(superAdminConnection))
    throw new InvalidOperationException("SuperAdmin database connection string 'DB_SUPERADMIN_CONNECTION' is required but not configured.");

builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(defaultConnection));

// Configure SuperAdmin Entity Framework (separate database)
builder.Services.AddDbContext<SuperAdminDbContext>(options =>
    options.UseSqlServer(superAdminConnection));

// Configure JWT Authentication with environment variable support
var secretKey = Environment.GetEnvironmentVariable("JWT_SECRET_KEY")
    ?? builder.Configuration["JwtSettings:SecretKey"];
var issuer = Environment.GetEnvironmentVariable("JWT_ISSUER")
    ?? builder.Configuration["JwtSettings:Issuer"];
var audience = Environment.GetEnvironmentVariable("JWT_AUDIENCE")
    ?? builder.Configuration["JwtSettings:Audience"];

if (string.IsNullOrEmpty(secretKey))
    throw new InvalidOperationException("JWT secret key 'JWT_SECRET_KEY' is required but not configured.");
if (string.IsNullOrEmpty(issuer))
    throw new InvalidOperationException("JWT issuer 'JWT_ISSUER' is required but not configured.");
if (string.IsNullOrEmpty(audience))
    throw new InvalidOperationException("JWT audience 'JWT_AUDIENCE' is required but not configured.");

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = issuer,
            ValidAudience = audience,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey))
        };
    });

// Add CORS with configurable origins
var corsOrigins = Environment.GetEnvironmentVariable("CORS_ORIGINS")?.Split(',')
    ?? new[] { "http://localhost:8080", "http://localhost:8081", "http://localhost:3000", "http://localhost:5173" };

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        policy.WithOrigins(corsOrigins)
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// Register services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<ISiteService, SiteService>();
builder.Services.AddScoped<IPersonalExpenseService, PersonalExpenseService>();
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<ISuperAdminAuthService, SuperAdminAuthService>();
builder.Services.AddScoped<IFirmManagementService, FirmManagementService>();
builder.Services.AddScoped<IAuditLogService, AuditLogService>();
builder.Services.AddScoped<ITenantContextService, TenantContextService>();
builder.Services.AddScoped<IDatabaseSeedingService, DatabaseSeedingService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowFrontend");
app.UseAuthentication();
app.UseMiddleware<TenantValidationMiddleware>();
app.UseAuthorization();

app.MapControllers();

// Ensure databases are created and seeded
using (var scope = app.Services.CreateScope())
{
    var applicationContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    var superAdminContext = scope.ServiceProvider.GetRequiredService<SuperAdminDbContext>();
    var seedingService = scope.ServiceProvider.GetRequiredService<IDatabaseSeedingService>();

    applicationContext.Database.EnsureCreated();
    superAdminContext.Database.EnsureCreated();

    // Seed initial data (SuperAdmin user)
    await seedingService.SeedInitialDataAsync();
}

app.Run();
