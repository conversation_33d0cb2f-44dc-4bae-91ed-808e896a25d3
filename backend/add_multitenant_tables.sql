-- Add new tables for multi-tenant support

-- Create Firms table
CREATE TABLE IF NOT EXISTS "Firms" (
    "Id" TEXT NOT NULL CONSTRAINT "PK_Firms" PRIMARY KEY,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Address" TEXT NOT NULL,
    "Contact" TEXT NOT NULL,
    "Email" TEXT NULL,
    "LogoUrl" TEXT NULL,
    "Website" TEXT NULL,
    "TaxId" TEXT NULL,
    "RegistrationNumber" TEXT NULL,
    "IsActive" INTEGER NOT NULL DEFAULT 1,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    "SubscriptionStatus" TEXT NOT NULL DEFAULT 'Active',
    "SubscriptionExpiryDate" TEXT NULL,
    "MaxUsers" INTEGER NOT NULL DEFAULT 10,
    "MaxSites" INTEGER NOT NULL DEFAULT 50
);

-- Create FirmSubscriptions table
CREATE TABLE IF NOT EXISTS "FirmSubscriptions" (
    "Id" TEXT NOT NULL CONSTRAINT "PK_FirmSubscriptions" PRIMARY KEY,
    "FirmId" TEXT NOT NULL,
    "PlanName" TEXT NOT NULL,
    "MonthlyPrice" REAL NOT NULL,
    "StartDate" TEXT NOT NULL,
    "EndDate" TEXT NOT NULL,
    "IsActive" INTEGER NOT NULL DEFAULT 1,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY ("FirmId") REFERENCES "Firms" ("Id") ON DELETE CASCADE
);

-- Create SuperAdmins table
CREATE TABLE IF NOT EXISTS "SuperAdmins" (
    "Id" TEXT NOT NULL CONSTRAINT "PK_SuperAdmins" PRIMARY KEY,
    "Email" TEXT NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FirstName" TEXT NOT NULL,
    "LastName" TEXT NOT NULL,
    "Phone" TEXT NULL,
    "IsActive" INTEGER NOT NULL DEFAULT 1,
    "IsFirstLogin" INTEGER NOT NULL DEFAULT 1,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    "LastLoginAt" TEXT NULL,
    "Role" TEXT NOT NULL DEFAULT 'SuperAdmin'
);

-- Create SuperAdminAuditLogs table
CREATE TABLE IF NOT EXISTS "SuperAdminAuditLogs" (
    "Id" TEXT NOT NULL CONSTRAINT "PK_SuperAdminAuditLogs" PRIMARY KEY,
    "SuperAdminId" TEXT NOT NULL,
    "Action" TEXT NOT NULL,
    "Description" TEXT NULL,
    "TargetType" TEXT NULL,
    "TargetId" TEXT NULL,
    "OldValues" TEXT NULL,
    "NewValues" TEXT NULL,
    "IpAddress" TEXT NULL,
    "UserAgent" TEXT NULL,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY ("SuperAdminId") REFERENCES "SuperAdmins" ("Id") ON DELETE CASCADE
);

-- Add new columns to Users table if they don't exist
ALTER TABLE "Users" ADD COLUMN "FirmId" TEXT DEFAULT '';
ALTER TABLE "Users" ADD COLUMN "UserType" TEXT DEFAULT 'FirmAdmin';

-- Create indexes
CREATE INDEX IF NOT EXISTS "IX_Firms_Name" ON "Firms" ("Name");
CREATE INDEX IF NOT EXISTS "IX_Firms_IsActive" ON "Firms" ("IsActive");
CREATE INDEX IF NOT EXISTS "IX_SuperAdmins_Email" ON "SuperAdmins" ("Email");
CREATE INDEX IF NOT EXISTS "IX_SuperAdmins_IsActive" ON "SuperAdmins" ("IsActive");
CREATE INDEX IF NOT EXISTS "IX_Users_FirmId" ON "Users" ("FirmId");
CREATE INDEX IF NOT EXISTS "IX_Users_UserType" ON "Users" ("UserType");

-- Create unique constraints
CREATE UNIQUE INDEX IF NOT EXISTS "IX_SuperAdmins_Email_Unique" ON "SuperAdmins" ("Email");

-- Insert a default SuperAdmin (you can change these credentials later)
INSERT OR IGNORE INTO "SuperAdmins" ("Id", "Email", "PasswordHash", "FirstName", "LastName", "IsFirstLogin", "IsActive")
VALUES (
    'superadmin-001',
    '<EMAIL>',
    '$2a$11$rQZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ', -- This is 'admin123' hashed
    'Super',
    'Admin',
    1,
    1
);
