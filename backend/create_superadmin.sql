-- DEPRECATED: Hardcoded SuperAdmin credentials have been removed for security
-- SuperAdmin users are now created dynamically through the DatabaseSeedingService
-- Configure the following environment variables to create the initial SuperAdmin:
-- SUPERADMIN_EMAIL=<EMAIL>
-- SUPERADMIN_PASSWORD=your-secure-password
-- SUPERADMIN_FIRST_NAME=Your First Name
-- SUPERADMIN_LAST_NAME=Your Last Name
-- SUPERADMIN_PHONE=******-0123 (optional)

-- The following INSERT statement has been commented out for security:
/*
INSERT OR IGNORE INTO "SuperAdmins" (
    "Id",
    "Email",
    "PasswordHash",
    "FirstName",
    "LastName",
    "Is<PERSON>irstLogin",
    "IsActive",
    "CreatedAt",
    "UpdatedAt"
) VALUES (
    'superadmin-001',
    '<EMAIL>',
    '$2a$11$rQZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ',
    'Super',
    'Admin',
    1,
    1,
    datetime('now'),
    datetime('now')
);
*/

-- Sample firm creation has been commented out - firms should be created through the SuperAdmin panel
/*
-- Create a sample firm for testing
INSERT OR IGNORE INTO "Firms" (
    "Id",
    "Name",
    "Address",
    "Contact",
    "Email",
    "IsActive",
    "SubscriptionStatus",
    "MaxUsers",
    "MaxSites",
    "CreatedAt",
    "UpdatedAt"
) VALUES (
    'firm-001',
    'Sample Construction Company',
    '123 Main Street, City, State 12345',
    '******-0123',
    '<EMAIL>',
    1,
    'Active',
    10,
    50,
    datetime('now'),
    datetime('now')
);
*/
