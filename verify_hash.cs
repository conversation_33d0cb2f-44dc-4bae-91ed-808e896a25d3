using System;

class Program
{
    static void Main()
    {
        string password = "admin123";
        string storedHash = "$2a$11$XRF4ux0D7y/4HRlA6LrnnOhFrORYojKtb.NZajA/QY9DEON1DeyeO";
        
        Console.WriteLine($"Password: {password}");
        Console.WriteLine($"Stored Hash: {storedHash}");
        
        bool isValid = BCrypt.Net.BCrypt.Verify(password, storedHash);
        Console.WriteLine($"Hash verification: {isValid}");
        
        // Test with wrong password
        bool isInvalid = BCrypt.Net.BCrypt.Verify("wrongpassword", storedHash);
        Console.WriteLine($"Wrong password verification: {isInvalid}");
    }
}
